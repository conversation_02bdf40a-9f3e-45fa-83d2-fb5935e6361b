<template>
  <div class="task-list-container">
    <div class="page-header">
      <h2>任务管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="goToCreate">
          <i class="el-icon-plus"></i>
          新建任务
        </el-button>
        <el-button @click="initData" :loading="initializing">
          初始化测试数据
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索任务标题、描述或编码"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px">
            <el-option label="待处理" value="PENDING" />
            <el-option label="已分配" value="ASSIGNED" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
            <el-option label="已逾期" value="OVERDUE" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="全部优先级" clearable style="width: 100px">
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
            <el-option label="极高" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.taskType" placeholder="全部类型" clearable style="width: 120px">
            <el-option label="问卷调查" value="SURVEY" />
            <el-option label="现场检查" value="INSPECTION" />
            <el-option label="数据采集" value="DATA_COLLECTION" />
            <el-option label="监测任务" value="MONITORING" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchTasks">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalTasks || 0 }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value pending">{{ stats.pendingTasks || 0 }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value progress">{{ stats.inProgressTasks || 0 }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value completed">{{ stats.completedTasks || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="taskList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="taskCode" label="任务编码" width="140" />
        
        <el-table-column prop="title" label="任务标题" min-width="200">
          <template slot-scope="scope">
            <div class="task-title">
              <span>{{ scope.row.title }}</span>
              <el-tag v-if="scope.row.isUrgent" type="danger" size="mini" style="margin-left: 8px">
                紧急
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="taskType" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTaskTypeColor(scope.row.taskType)" size="small">
              {{ getTaskTypeName(scope.row.taskType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="priority" label="优先级" width="80">
          <template slot-scope="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
              {{ getPriorityName(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="progress" label="进度" width="100">
          <template slot-scope="scope">
            <el-progress
              :percentage="scope.row.progress || 0"
              :stroke-width="6"
              :show-text="false"
            />
            <span style="margin-left: 8px; font-size: 12px;">{{ scope.row.progress || 0 }}%</span>
          </template>
        </el-table-column>

        <el-table-column prop="deadline" label="截止时间" width="160">
          <template slot-scope="scope">
            <span v-if="scope.row.deadline">
              {{ formatDate(scope.row.deadline) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="createDate" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewTask(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" @click="editTask(scope.row)">编辑</el-button>
            <el-dropdown @command="handleCommand" style="margin-left: 8px">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'assign', row: scope.row}">分配</el-dropdown-item>
                <el-dropdown-item :command="{action: 'start', row: scope.row}">开始</el-dropdown-item>
                <el-dropdown-item :command="{action: 'complete', row: scope.row}">完成</el-dropdown-item>
                <el-dropdown-item :command="{action: 'cancel', row: scope.row}">取消</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { getTaskList, getTaskStats, initTestData, assignTask, startTask, completeTask, cancelTask } from './api/city-task'

export default {
  name: 'TaskList',
  data() {
    return {
      loading: false,
      initializing: false,
      taskList: [],
      selectedTasks: [],
      stats: {},
      searchForm: {
        keyword: '',
        status: '',
        priority: null,
        taskType: ''
      },
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  created() {
    this.loadTaskList()
    this.loadStats()
  },
  methods: {
    async loadTaskList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }
        
        const response = await getTaskList(params)
        if (response.resultCode === 200) {
          this.taskList = response.data.result || []
          this.pagination.total = response.data.totalItems || 0
        }
      } catch (error) {
        this.$message.error('加载任务列表失败')
      } finally {
        this.loading = false
      }
    },
    async loadStats() {
      try {
        const response = await getTaskStats()
        if (response.resultCode === 200) {
          this.stats = response.data.totalStats || {}
        }
      } catch (error) {
        console.error('加载统计数据失败', error)
      }
    },
    async initData() {
      this.initializing = true
      try {
        const response = await initTestData()
        if (response.resultCode === 200) {
          this.$message.success('测试数据初始化成功')
          this.loadTaskList()
          this.loadStats()
        } else {
          this.$message.error(response.resultMsg || '初始化失败')
        }
      } catch (error) {
        this.$message.error('初始化测试数据失败')
      } finally {
        this.initializing = false
      }
    },
    searchTasks() {
      this.pagination.page = 1
      this.loadTaskList()
    },
    resetSearch() {
      this.searchForm = {
        keyword: '',
        status: '',
        priority: null,
        taskType: ''
      }
      this.searchTasks()
    },
    handleSelectionChange(selection) {
      this.selectedTasks = selection
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.loadTaskList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadTaskList()
    },
    goToCreate() {
      this.$router.push('/v6/dw/task/create')
    },
    viewTask(task) {
      this.$router.push(`/v6/dw/task/detail/${task.id}`)
    },
    editTask(task) {
      this.$router.push(`/v6/dw/task/edit/${task.id}`)
    },
    async handleCommand(command) {
      const { action, row } = command
      
      switch (action) {
        case 'assign':
          // TODO: 实现分配任务对话框
          this.$message.info('分配功能开发中')
          break
        case 'start':
          await this.startTask(row)
          break
        case 'complete':
          await this.completeTask(row)
          break
        case 'cancel':
          await this.cancelTask(row)
          break
      }
    },
    async startTask(task) {
      try {
        const response = await startTask(task.id)
        if (response.resultCode === 200) {
          this.$message.success('任务已开始')
          this.loadTaskList()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },
    async completeTask(task) {
      try {
        const response = await completeTask(task.id, '')
        if (response.resultCode === 200) {
          this.$message.success('任务已完成')
          this.loadTaskList()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },
    async cancelTask(task) {
      try {
        await this.$confirm('确定要取消这个任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await cancelTask(task.id, '用户取消')
        if (response.resultCode === 200) {
          this.$message.success('任务已取消')
          this.loadTaskList()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('操作失败')
        }
      }
    },
    getTaskTypeName(type) {
      const types = {
        'SURVEY': '问卷调查',
        'INSPECTION': '现场检查',
        'DATA_COLLECTION': '数据采集',
        'MONITORING': '监测任务'
      }
      return types[type] || type
    },
    getTaskTypeColor(type) {
      const colors = {
        'SURVEY': 'primary',
        'INSPECTION': 'success',
        'DATA_COLLECTION': 'warning',
        'MONITORING': 'info'
      }
      return colors[type] || ''
    },
    getPriorityName(priority) {
      const priorities = { 1: '低', 2: '中', 3: '高', 4: '极高' }
      return priorities[priority] || priority
    },
    getPriorityColor(priority) {
      const colors = { 1: 'info', 2: '', 3: 'warning', 4: 'danger' }
      return colors[priority] || ''
    },
    getStatusName(status) {
      const statuses = {
        'PENDING': '待处理',
        'ASSIGNED': '已分配',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消',
        'OVERDUE': '已逾期'
      }
      return statuses[status] || status
    },
    getStatusColor(status) {
      const colors = {
        'PENDING': 'info',
        'ASSIGNED': 'primary',
        'IN_PROGRESS': 'warning',
        'COMPLETED': 'success',
        'CANCELLED': 'info',
        'OVERDUE': 'danger'
      }
      return colors[status] || ''
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.task-list-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-value.pending {
  color: #909399;
}

.stat-value.progress {
  color: #e6a23c;
}

.stat-value.completed {
  color: #67c23a;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.table-card {
  background: white;
}

.task-title {
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .task-list-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .stats-row .el-col {
    margin-bottom: 10px;
  }
}
</style>
