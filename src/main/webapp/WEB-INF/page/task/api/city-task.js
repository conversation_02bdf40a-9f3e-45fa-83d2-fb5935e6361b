import request from '@/utils/request'

const API_BASE = '/api/dwsurvey/app/city-task'

/**
 * 获取任务列表
 */
export function getTaskList(params) {
  return request({
    url: `${API_BASE}/list.do`,
    method: 'get',
    params
  })
}

/**
 * 获取任务详情
 */
export function getTaskDetail(id) {
  return request({
    url: `${API_BASE}/info.do`,
    method: 'get',
    params: { id }
  })
}

/**
 * 创建任务
 */
export function createTask(data) {
  return request({
    url: `${API_BASE}/create.do`,
    method: 'post',
    data
  })
}

/**
 * 更新任务
 */
export function updateTask(data) {
  return request({
    url: `${API_BASE}/update.do`,
    method: 'post',
    data
  })
}

/**
 * 删除任务
 */
export function deleteTask(id) {
  return request({
    url: `${API_BASE}/delete.do`,
    method: 'post',
    data: { id }
  })
}

/**
 * 分配任务
 */
export function assignTask(taskId, assigneeId) {
  return request({
    url: `${API_BASE}/assign.do`,
    method: 'post',
    data: {
      taskId,
      assigneeId
    }
  })
}

/**
 * 批量分配任务
 */
export function batchAssignTasks(taskIds, assigneeId) {
  return request({
    url: `${API_BASE}/batch-assign.do`,
    method: 'post',
    data: {
      taskIds,
      assigneeId
    }
  })
}

/**
 * 开始任务
 */
export function startTask(taskId) {
  return request({
    url: `${API_BASE}/start.do`,
    method: 'post',
    data: { taskId }
  })
}

/**
 * 完成任务
 */
export function completeTask(taskId, resultData) {
  return request({
    url: `${API_BASE}/complete.do`,
    method: 'post',
    data: {
      taskId,
      resultData
    }
  })
}

/**
 * 取消任务
 */
export function cancelTask(taskId, reason) {
  return request({
    url: `${API_BASE}/cancel.do`,
    method: 'post',
    data: {
      taskId,
      reason
    }
  })
}

/**
 * 更新任务状态
 */
export function updateTaskStatus(taskId, status) {
  return request({
    url: `${API_BASE}/update-status.do`,
    method: 'post',
    data: {
      taskId,
      status
    }
  })
}

/**
 * 获取我的待办任务
 */
export function getMyPendingTasks() {
  return request({
    url: `${API_BASE}/my-pending.do`,
    method: 'get'
  })
}

/**
 * 获取紧急任务
 */
export function getUrgentTasks() {
  return request({
    url: `${API_BASE}/urgent.do`,
    method: 'get'
  })
}

/**
 * 获取逾期任务
 */
export function getOverdueTasks() {
  return request({
    url: `${API_BASE}/overdue.do`,
    method: 'get'
  })
}

/**
 * 获取推送通知列表
 */
export function getPushList() {
  return request({
    url: `${API_BASE}/push-list.do`,
    method: 'get'
  })
}

/**
 * 获取任务类型列表
 */
export function getTaskTypes() {
  return request({
    url: `${API_BASE}/types.do`,
    method: 'get'
  })
}

/**
 * 获取任务统计
 */
export function getTaskStatistics(params) {
  return request({
    url: `${API_BASE}/statistics.do`,
    method: 'get',
    params
  })
}

/**
 * 获取任务统计数据
 */
export function getTaskStats() {
  return request({
    url: `${API_BASE}/stats.do`,
    method: 'get'
  })
}

/**
 * 搜索任务
 */
export function searchTasks(keyword, page = 1, pageSize = 10) {
  return request({
    url: `${API_BASE}/search.do`,
    method: 'get',
    params: {
      keyword,
      page,
      pageSize
    }
  })
}

/**
 * 获取任务创建表单配置
 */
export function getTaskFormConfig() {
  return request({
    url: `${API_BASE}/form-config.do`,
    method: 'get'
  })
}

/**
 * 获取网格列表（用于任务创建）
 */
export function getGridsForTask() {
  return request({
    url: `${API_BASE}/grids.do`,
    method: 'get'
  })
}

/**
 * 获取用户列表（用于任务分配）
 */
export function getUsersForTask(params) {
  return request({
    url: `${API_BASE}/users.do`,
    method: 'get',
    params
  })
}

/**
 * 初始化测试数据
 */
export function initTestData() {
  return request({
    url: `${API_BASE}/init-data.do`,
    method: 'post'
  })
}

export default {
  getTaskList,
  getTaskDetail,
  createTask,
  updateTask,
  deleteTask,
  assignTask,
  batchAssignTasks,
  startTask,
  completeTask,
  cancelTask,
  updateTaskStatus,
  getMyPendingTasks,
  getUrgentTasks,
  getOverdueTasks,
  getPushList,
  getTaskTypes,
  getTaskStatistics,
  getTaskStats,
  searchTasks,
  getTaskFormConfig,
  getGridsForTask,
  getUsersForTask,
  initTestData
}
