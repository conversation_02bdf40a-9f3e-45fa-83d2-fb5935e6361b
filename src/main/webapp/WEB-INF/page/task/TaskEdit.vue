<template>
  <div class="task-edit-container">
    <div class="page-header">
      <h2>编辑任务</h2>
      <div class="header-actions">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="submitTask" :loading="submitting">
          {{ submitting ? '保存中...' : '保存任务' }}
        </el-button>
      </div>
    </div>

    <div v-loading="loading">
      <!-- 复用TaskCreate组件的表单结构，但修改为编辑模式 -->
      <el-form
        ref="taskForm"
        :model="taskForm"
        :rules="formRules"
        label-width="120px"
        class="task-form"
      >
        <el-card class="form-section">
          <div slot="header">
            <span>基本信息</span>
          </div>
          
          <el-form-item label="任务标题" prop="title">
            <el-input
              v-model="taskForm.title"
              placeholder="请输入任务标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="taskForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入任务描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="任务类型" prop="taskType">
                <el-select v-model="taskForm.taskType" placeholder="请选择任务类型" style="width: 100%">
                  <el-option
                    v-for="type in taskTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务分类" prop="category">
                <el-select v-model="taskForm.category" placeholder="请选择任务分类" style="width: 100%">
                  <el-option
                    v-for="category in categories"
                    :key="category.value"
                    :label="category.label"
                    :value="category.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="优先级" prop="priority">
                <el-select v-model="taskForm.priority" placeholder="请选择优先级" style="width: 100%">
                  <el-option
                    v-for="priority in priorities"
                    :key="priority.value"
                    :label="priority.label"
                    :value="priority.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否紧急">
                <el-switch
                  v-model="taskForm.isUrgent"
                  active-text="紧急"
                  inactive-text="普通"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="form-section">
          <div slot="header">
            <span>分配信息</span>
          </div>

          <el-form-item label="所属网格" prop="gridId">
            <el-select
              v-model="taskForm.gridId"
              placeholder="请选择网格"
              style="width: 100%"
              @change="onGridChange"
            >
              <el-option
                v-for="grid in grids"
                :key="grid.id"
                :label="`${grid.gridName} (${grid.gridCode})`"
                :value="grid.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="负责人" prop="assigneeId">
            <el-select
              v-model="taskForm.assigneeId"
              placeholder="请选择负责人（可选）"
              style="width: 100%"
              clearable
              :loading="loadingUsers"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.name || user.loginName"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-card>

        <el-card class="form-section">
          <div slot="header">
            <span>时间安排</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="taskForm.startDate"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="截止时间" prop="deadline">
                <el-date-picker
                  v-model="taskForm.deadline"
                  type="datetime"
                  placeholder="选择截止时间"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="预计耗时">
            <el-input-number
              v-model="taskForm.estimatedDuration"
              :min="30"
              :max="1440"
              :step="30"
              placeholder="预计耗时（分钟）"
            />
            <span style="margin-left: 10px; color: #8492a6;">分钟</span>
          </el-form-item>
        </el-card>

        <el-card class="form-section">
          <div slot="header">
            <span>位置信息</span>
          </div>

          <el-form-item label="任务地址">
            <el-input
              v-model="taskForm.locationAddress"
              placeholder="请输入任务执行地址"
              maxlength="200"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度">
                <el-input-number
                  v-model="taskForm.locationLongitude"
                  :precision="6"
                  :step="0.000001"
                  placeholder="经度"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度">
                <el-input-number
                  v-model="taskForm.locationLatitude"
                  :precision="6"
                  :step="0.000001"
                  placeholder="纬度"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="form-section">
          <div slot="header">
            <span>其他设置</span>
          </div>

          <el-form-item label="标签">
            <el-input
              v-model="taskForm.tags"
              placeholder="请输入标签，多个标签用逗号分隔"
              maxlength="200"
            />
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="taskForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="循环任务">
            <el-switch
              v-model="taskForm.isRecurring"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>

          <el-form-item v-if="taskForm.isRecurring" label="循环规则">
            <el-input
              v-model="taskForm.recurringRule"
              placeholder="例如：每日、每周一、每月1日等"
              maxlength="100"
            />
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getTaskDetail, updateTask, getTaskFormConfig, getGridsForTask, getUsersForTask } from './api/city-task'

export default {
  name: 'TaskEdit',
  data() {
    return {
      loading: false,
      submitting: false,
      loadingUsers: false,
      taskForm: {
        id: '',
        title: '',
        description: '',
        taskType: '',
        category: '',
        priority: 2,
        gridId: '',
        assigneeId: '',
        startDate: '',
        deadline: '',
        estimatedDuration: 120,
        locationAddress: '',
        locationLongitude: null,
        locationLatitude: null,
        tags: '',
        remarks: '',
        isUrgent: false,
        isRecurring: false,
        recurringRule: ''
      },
      taskTypes: [],
      categories: [],
      priorities: [],
      grids: [],
      users: [],
      formRules: {
        title: [
          { required: true, message: '请输入任务标题', trigger: 'blur' },
          { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入任务描述', trigger: 'blur' },
          { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        taskType: [
          { required: true, message: '请选择任务类型', trigger: 'change' }
        ],
        gridId: [
          { required: true, message: '请选择所属网格', trigger: 'change' }
        ],
        deadline: [
          { required: true, message: '请选择截止时间', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.loadFormConfig()
    this.loadGrids()
    this.loadTaskDetail()
  },
  methods: {
    async loadTaskDetail() {
      this.loading = true
      try {
        const taskId = this.$route.params.id
        const response = await getTaskDetail(taskId)
        if (response.resultCode === 200) {
          const task = response.data || {}
          
          // 填充表单数据
          this.taskForm = {
            id: task.id,
            title: task.title || '',
            description: task.description || '',
            taskType: task.taskType || '',
            category: task.category || '',
            priority: task.priority || 2,
            gridId: task.gridId || '',
            assigneeId: task.assigneeId || '',
            startDate: task.startDate ? this.formatDateForInput(task.startDate) : '',
            deadline: task.deadline ? this.formatDateForInput(task.deadline) : '',
            estimatedDuration: task.estimatedDuration || 120,
            locationAddress: task.locationAddress || '',
            locationLongitude: task.locationLongitude || null,
            locationLatitude: task.locationLatitude || null,
            tags: task.tags || '',
            remarks: task.remarks || '',
            isUrgent: task.isUrgent || false,
            isRecurring: task.isRecurring || false,
            recurringRule: task.recurringRule || ''
          }

          // 如果有网格ID，加载对应的用户列表
          if (this.taskForm.gridId) {
            this.loadUsers(this.taskForm.gridId)
          }
        } else {
          this.$message.error(response.resultMsg || '加载任务详情失败')
        }
      } catch (error) {
        this.$message.error('加载任务详情失败')
      } finally {
        this.loading = false
      }
    },
    async loadFormConfig() {
      try {
        const response = await getTaskFormConfig()
        if (response.resultCode === 200) {
          this.taskTypes = response.data.taskTypes || []
          this.categories = response.data.categories || []
          this.priorities = response.data.priorities || []
        }
      } catch (error) {
        this.$message.error('加载表单配置失败')
      }
    },
    async loadGrids() {
      try {
        const response = await getGridsForTask()
        if (response.resultCode === 200) {
          this.grids = response.data || []
        }
      } catch (error) {
        this.$message.error('加载网格列表失败')
      }
    },
    async onGridChange(gridId) {
      if (gridId) {
        this.loadUsers(gridId)
      } else {
        this.users = []
        this.taskForm.assigneeId = ''
      }
    },
    async loadUsers(gridId) {
      this.loadingUsers = true
      try {
        const response = await getUsersForTask({ gridId })
        if (response.resultCode === 200) {
          this.users = response.data || []
        }
      } catch (error) {
        this.$message.error('加载用户列表失败')
      } finally {
        this.loadingUsers = false
      }
    },
    async submitTask() {
      try {
        const valid = await this.$refs.taskForm.validate()
        if (!valid) return

        this.submitting = true

        // 数据预处理
        const taskData = { ...this.taskForm }
        
        // 转换日期格式
        if (taskData.startDate) {
          taskData.startDate = new Date(taskData.startDate)
        }
        if (taskData.deadline) {
          taskData.deadline = new Date(taskData.deadline)
        }

        const response = await updateTask(taskData)
        
        if (response.resultCode === 200) {
          this.$message.success('任务更新成功')
          this.$router.push(`/v6/dw/task/detail/${taskData.id}`)
        } else {
          this.$message.error(response.resultMsg || '更新任务失败')
        }
      } catch (error) {
        this.$message.error('更新任务失败：' + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    formatDateForInput(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.getFullYear() + '-' + 
             String(d.getMonth() + 1).padStart(2, '0') + '-' + 
             String(d.getDate()).padStart(2, '0') + ' ' +
             String(d.getHours()).padStart(2, '0') + ':' + 
             String(d.getMinutes()).padStart(2, '0') + ':' + 
             String(d.getSeconds()).padStart(2, '0')
    }
  }
}
</script>

<style scoped>
.task-edit-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.task-form {
  max-width: 1200px;
}

.form-section {
  margin-bottom: 20px;
}

.form-section .el-card__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-select, .el-date-picker {
  width: 100%;
}

@media (max-width: 768px) {
  .task-edit-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
