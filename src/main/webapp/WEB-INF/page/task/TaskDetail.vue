<template>
  <div class="task-detail-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <h2>任务详情</h2>
      </div>
      <div class="header-actions">
        <el-button @click="editTask">编辑</el-button>
        <el-dropdown @command="handleCommand" style="margin-left: 8px">
          <el-button type="primary">
            操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="assign">分配任务</el-dropdown-item>
            <el-dropdown-item command="start">开始任务</el-dropdown-item>
            <el-dropdown-item command="complete">完成任务</el-dropdown-item>
            <el-dropdown-item command="cancel">取消任务</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <el-row :gutter="20">
        <el-col :span="16">
          <!-- 基本信息 -->
          <el-card class="detail-card">
            <div slot="header">
              <span>基本信息</span>
            </div>
            
            <div class="task-header">
              <h3>{{ task.title }}</h3>
              <div class="task-badges">
                <el-tag v-if="task.isUrgent" type="danger">紧急</el-tag>
                <el-tag :type="getStatusColor(task.status)">{{ getStatusName(task.status) }}</el-tag>
                <el-tag :type="getPriorityColor(task.priority)">{{ getPriorityName(task.priority) }}</el-tag>
              </div>
            </div>

            <div class="detail-item">
              <label>任务编码：</label>
              <span>{{ task.taskCode }}</span>
            </div>

            <div class="detail-item">
              <label>任务描述：</label>
              <p>{{ task.description }}</p>
            </div>

            <div class="detail-item">
              <label>任务类型：</label>
              <el-tag :type="getTaskTypeColor(task.taskType)">{{ getTaskTypeName(task.taskType) }}</el-tag>
            </div>

            <div class="detail-item" v-if="task.category">
              <label>任务分类：</label>
              <span>{{ task.category }}</span>
            </div>

            <div class="detail-item">
              <label>任务进度：</label>
              <el-progress :percentage="task.progress || 0" :stroke-width="8" />
            </div>

            <div class="detail-item" v-if="task.tags">
              <label>标签：</label>
              <el-tag
                v-for="tag in getTagList(task.tags)"
                :key="tag"
                size="small"
                style="margin-right: 8px"
              >
                {{ tag }}
              </el-tag>
            </div>

            <div class="detail-item" v-if="task.remarks">
              <label>备注：</label>
              <p>{{ task.remarks }}</p>
            </div>
          </el-card>

          <!-- 位置信息 -->
          <el-card class="detail-card" v-if="task.locationAddress || task.locationLongitude">
            <div slot="header">
              <span>位置信息</span>
            </div>
            
            <div class="detail-item" v-if="task.locationAddress">
              <label>任务地址：</label>
              <span>{{ task.locationAddress }}</span>
            </div>

            <div class="detail-item" v-if="task.locationLongitude && task.locationLatitude">
              <label>坐标位置：</label>
              <span>{{ task.locationLongitude }}, {{ task.locationLatitude }}</span>
            </div>
          </el-card>

          <!-- 循环设置 -->
          <el-card class="detail-card" v-if="task.isRecurring">
            <div slot="header">
              <span>循环设置</span>
            </div>
            
            <div class="detail-item">
              <label>循环任务：</label>
              <el-tag type="info">是</el-tag>
            </div>

            <div class="detail-item" v-if="task.recurringRule">
              <label>循环规则：</label>
              <span>{{ task.recurringRule }}</span>
            </div>
          </el-card>

          <!-- 执行结果 -->
          <el-card class="detail-card" v-if="task.resultData">
            <div slot="header">
              <span>执行结果</span>
            </div>
            
            <div class="detail-item">
              <label>结果数据：</label>
              <pre>{{ task.resultData }}</pre>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <!-- 状态信息 -->
          <el-card class="detail-card">
            <div slot="header">
              <span>状态信息</span>
            </div>
            
            <div class="status-timeline">
              <el-timeline>
                <el-timeline-item
                  v-if="task.createDate"
                  timestamp="创建时间"
                  :color="'#909399'"
                >
                  {{ formatDate(task.createDate) }}
                </el-timeline-item>
                
                <el-timeline-item
                  v-if="task.assignDate"
                  timestamp="分配时间"
                  :color="'#409eff'"
                >
                  {{ formatDate(task.assignDate) }}
                </el-timeline-item>
                
                <el-timeline-item
                  v-if="task.startDate"
                  timestamp="开始时间"
                  :color="'#e6a23c'"
                >
                  {{ formatDate(task.startDate) }}
                </el-timeline-item>
                
                <el-timeline-item
                  v-if="task.completedDate"
                  timestamp="完成时间"
                  :color="'#67c23a'"
                >
                  {{ formatDate(task.completedDate) }}
                </el-timeline-item>
                
                <el-timeline-item
                  v-if="task.deadline"
                  timestamp="截止时间"
                  :color="isOverdue(task.deadline) ? '#f56c6c' : '#909399'"
                >
                  {{ formatDate(task.deadline) }}
                  <el-tag v-if="isOverdue(task.deadline)" type="danger" size="mini">已逾期</el-tag>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>

          <!-- 分配信息 -->
          <el-card class="detail-card">
            <div slot="header">
              <span>分配信息</span>
            </div>
            
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ task.creatorName || task.creatorId }}</span>
            </div>

            <div class="detail-item" v-if="task.assigneeId">
              <label>负责人：</label>
              <span>{{ task.assigneeName || task.assigneeId }}</span>
            </div>

            <div class="detail-item" v-if="task.gridId">
              <label>所属网格：</label>
              <span>{{ task.gridName || task.gridId }}</span>
            </div>
          </el-card>

          <!-- 时间信息 -->
          <el-card class="detail-card">
            <div slot="header">
              <span>时间信息</span>
            </div>
            
            <div class="detail-item">
              <label>预计耗时：</label>
              <span>{{ task.estimatedDuration || 0 }} 分钟</span>
            </div>

            <div class="detail-item" v-if="task.actualDuration">
              <label>实际耗时：</label>
              <span>{{ task.actualDuration }} 分钟</span>
            </div>

            <div class="detail-item" v-if="task.updateDate">
              <label>最后更新：</label>
              <span>{{ formatDate(task.updateDate) }}</span>
            </div>
          </el-card>

          <!-- 附件信息 -->
          <el-card class="detail-card" v-if="task.attachments">
            <div slot="header">
              <span>附件信息</span>
            </div>
            
            <div class="detail-item">
              <label>附件：</label>
              <div>{{ task.attachments }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getTaskDetail, assignTask, startTask, completeTask, cancelTask } from './api/city-task'

export default {
  name: 'TaskDetail',
  data() {
    return {
      loading: false,
      task: {}
    }
  },
  created() {
    this.loadTaskDetail()
  },
  methods: {
    async loadTaskDetail() {
      this.loading = true
      try {
        const taskId = this.$route.params.id
        const response = await getTaskDetail(taskId)
        if (response.resultCode === 200) {
          this.task = response.data || {}
        } else {
          this.$message.error(response.resultMsg || '加载任务详情失败')
        }
      } catch (error) {
        this.$message.error('加载任务详情失败')
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    editTask() {
      this.$router.push(`/v6/dw/task/edit/${this.task.id}`)
    },
    async handleCommand(command) {
      switch (command) {
        case 'assign':
          this.$message.info('分配功能开发中')
          break
        case 'start':
          await this.startTask()
          break
        case 'complete':
          await this.completeTask()
          break
        case 'cancel':
          await this.cancelTask()
          break
      }
    },
    async startTask() {
      try {
        const response = await startTask(this.task.id)
        if (response.resultCode === 200) {
          this.$message.success('任务已开始')
          this.loadTaskDetail()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },
    async completeTask() {
      try {
        const response = await completeTask(this.task.id, '')
        if (response.resultCode === 200) {
          this.$message.success('任务已完成')
          this.loadTaskDetail()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },
    async cancelTask() {
      try {
        await this.$confirm('确定要取消这个任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await cancelTask(this.task.id, '用户取消')
        if (response.resultCode === 200) {
          this.$message.success('任务已取消')
          this.loadTaskDetail()
        } else {
          this.$message.error(response.resultMsg || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('操作失败')
        }
      }
    },
    getTaskTypeName(type) {
      const types = {
        'SURVEY': '问卷调查',
        'INSPECTION': '现场检查',
        'DATA_COLLECTION': '数据采集',
        'MONITORING': '监测任务'
      }
      return types[type] || type
    },
    getTaskTypeColor(type) {
      const colors = {
        'SURVEY': 'primary',
        'INSPECTION': 'success',
        'DATA_COLLECTION': 'warning',
        'MONITORING': 'info'
      }
      return colors[type] || ''
    },
    getPriorityName(priority) {
      const priorities = { 1: '低', 2: '中', 3: '高', 4: '极高' }
      return priorities[priority] || priority
    },
    getPriorityColor(priority) {
      const colors = { 1: 'info', 2: '', 3: 'warning', 4: 'danger' }
      return colors[priority] || ''
    },
    getStatusName(status) {
      const statuses = {
        'PENDING': '待处理',
        'ASSIGNED': '已分配',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消',
        'OVERDUE': '已逾期'
      }
      return statuses[status] || status
    },
    getStatusColor(status) {
      const colors = {
        'PENDING': 'info',
        'ASSIGNED': 'primary',
        'IN_PROGRESS': 'warning',
        'COMPLETED': 'success',
        'CANCELLED': 'info',
        'OVERDUE': 'danger'
      }
      return colors[status] || ''
    },
    getTagList(tags) {
      if (!tags) return []
      return tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },
    isOverdue(deadline) {
      if (!deadline) return false
      return new Date(deadline) < new Date()
    }
  }
}
</script>

<style scoped>
.task-detail-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.detail-content {
  min-height: 400px;
}

.detail-card {
  margin-bottom: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.task-header h3 {
  margin: 0;
  color: #303133;
  flex: 1;
}

.task-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.detail-item span,
.detail-item p {
  color: #303133;
  margin: 0;
  flex: 1;
}

.detail-item pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.status-timeline {
  padding: 10px 0;
}

@media (max-width: 768px) {
  .task-detail-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .header-left {
    width: 100%;
    justify-content: flex-start;
  }
  
  .header-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  
  .task-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .detail-item {
    flex-direction: column;
  }
  
  .detail-item label {
    min-width: auto;
    margin-bottom: 5px;
  }
}
</style>
