// 任务管理路由配置
export const taskRoutes = [
  {
    path: '/v6/dw/task',
    name: 'TaskManagement',
    component: () => import('@/layout/index'),
    redirect: '/v6/dw/task/list',
    meta: {
      title: '任务管理',
      icon: 'task'
    },
    children: [
      {
        path: 'list',
        name: 'TaskList',
        component: () => import('../TaskList.vue'),
        meta: {
          title: '任务列表',
          icon: 'list'
        }
      },
      {
        path: 'create',
        name: 'TaskCreate',
        component: () => import('../TaskCreate.vue'),
        meta: {
          title: '新建任务',
          icon: 'plus'
        }
      },
      {
        path: 'edit/:id',
        name: 'TaskEdit',
        component: () => import('../TaskEdit.vue'),
        meta: {
          title: '编辑任务',
          icon: 'edit',
          hidden: true
        }
      },
      {
        path: 'detail/:id',
        name: 'TaskDetail',
        component: () => import('../TaskDetail.vue'),
        meta: {
          title: '任务详情',
          icon: 'view',
          hidden: true
        }
      }
    ]
  }
]

export default taskRoutes
