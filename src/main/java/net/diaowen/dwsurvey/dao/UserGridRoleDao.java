package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.UserGridRole;

import java.util.List;
import java.util.Map;

/**
 * 用户网格角色数据访问接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface UserGridRoleDao extends BaseDao<UserGridRole, String> {

    /**
     * 根据条件分页查询用户角色列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<UserGridRole> findRolesByCondition(Page<UserGridRole> page, Map<String, Object> params);

    /**
     * 根据用户ID查询角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<UserGridRole> findByUserId(String userId);

    /**
     * 根据网格ID查询角色列表
     * @param gridId 网格ID
     * @return 角色列表
     */
    List<UserGridRole> findByGridId(String gridId);

    /**
     * 根据角色代码查询角色列表
     * @param roleCode 角色代码
     * @return 角色列表
     */
    List<UserGridRole> findByRoleCode(String roleCode);

    /**
     * 根据用户ID和网格ID查询角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 角色列表
     */
    List<UserGridRole> findByUserIdAndGridId(String userId, String gridId);

    /**
     * 根据用户ID、网格ID和角色代码查询角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @return 角色对象
     */
    UserGridRole findByUserIdAndGridIdAndRoleCode(String userId, String gridId, String roleCode);

    /**
     * 查询用户在指定网格的权限
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 权限列表
     */
    List<String> findUserPermissions(String userId, String gridId);

    /**
     * 查询网格管理员列表
     * @param gridId 网格ID
     * @return 管理员列表
     */
    List<UserGridRole> findGridAdmins(String gridId);

    /**
     * 查询网格负责人列表
     * @param gridId 网格ID
     * @return 负责人列表
     */
    List<UserGridRole> findGridManagers(String gridId);

    /**
     * 查询数据采集员列表
     * @param gridId 网格ID
     * @return 采集员列表
     */
    List<UserGridRole> findDataCollectors(String gridId);

    /**
     * 检查用户是否有指定网格的权限
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param permission 权限代码
     * @return 是否有权限
     */
    boolean hasPermission(String userId, String gridId, String permission);

    /**
     * 检查用户是否有指定角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @return 是否有角色
     */
    boolean hasRole(String userId, String gridId, String roleCode);

    /**
     * 删除用户在指定网格的所有角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 删除数量
     */
    int deleteUserGridRoles(String userId, String gridId);

    /**
     * 删除用户的指定角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @return 删除数量
     */
    int deleteUserRole(String userId, String gridId, String roleCode);

    /**
     * 批量分配角色
     * @param userIds 用户ID列表
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @param creatorId 创建人ID
     * @return 分配数量
     */
    int batchAssignRoles(List<String> userIds, String gridId, String roleCode, String creatorId);

    /**
     * 更新角色状态
     * @param id 角色ID
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新结果
     */
    int updateRoleStatus(String id, Integer status, String updaterId);
}
