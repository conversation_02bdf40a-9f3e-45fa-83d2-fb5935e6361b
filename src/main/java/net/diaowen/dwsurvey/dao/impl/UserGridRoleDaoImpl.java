package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.UserGridRoleDao;
import net.diaowen.dwsurvey.entity.UserGridRole;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 用户网格角色数据访问实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Repository
public class UserGridRoleDaoImpl extends BaseDaoImpl<UserGridRole, String> implements UserGridRoleDao {

    @Override
    public Page<UserGridRole> findRolesByCondition(Page<UserGridRole> page, Map<String, Object> params) {
        StringBuilder hql = new StringBuilder("FROM UserGridRole r WHERE r.status = 1");
        List<Object> paramList = new ArrayList<>();

        if (params.get("userId") != null) {
            hql.append(" AND r.userId = ?");
            paramList.add(params.get("userId"));
        }

        if (params.get("gridId") != null) {
            hql.append(" AND r.gridId = ?");
            paramList.add(params.get("gridId"));
        }

        if (params.get("roleCode") != null) {
            hql.append(" AND r.roleCode = ?");
            paramList.add(params.get("roleCode"));
        }

        if (params.get("keyword") != null && StringUtils.isNotBlank((String) params.get("keyword"))) {
            hql.append(" AND (r.roleName LIKE ? OR r.roleCode LIKE ?)");
            String keyword = "%" + params.get("keyword") + "%";
            paramList.add(keyword);
            paramList.add(keyword);
        }

        hql.append(" ORDER BY r.assignDate DESC");

        return findPage(page, hql.toString(), paramList.toArray());
    }

    @Override
    public List<UserGridRole> findByUserId(String userId) {
        String hql = "FROM UserGridRole r WHERE r.userId = ? AND r.status = 1 ORDER BY r.assignDate DESC";
        return find(hql, userId);
    }

    @Override
    public List<UserGridRole> findByGridId(String gridId) {
        String hql = "FROM UserGridRole r WHERE r.gridId = ? AND r.status = 1 ORDER BY r.assignDate DESC";
        return find(hql, gridId);
    }

    @Override
    public List<UserGridRole> findByRoleCode(String roleCode) {
        String hql = "FROM UserGridRole r WHERE r.roleCode = ? AND r.status = 1 ORDER BY r.assignDate DESC";
        return find(hql, roleCode);
    }

    @Override
    public List<UserGridRole> findByUserIdAndGridId(String userId, String gridId) {
        String hql = "FROM UserGridRole r WHERE r.userId = ? AND r.gridId = ? AND r.status = 1 ORDER BY r.assignDate DESC";
        return find(hql, userId, gridId);
    }

    @Override
    public UserGridRole findByUserIdAndGridIdAndRoleCode(String userId, String gridId, String roleCode) {
        String hql = "FROM UserGridRole r WHERE r.userId = ? AND r.gridId = ? AND r.roleCode = ? AND r.status = 1";
        return findUnique(hql, userId, gridId, roleCode);
    }

    @Override
    public List<String> findUserPermissions(String userId, String gridId) {
        String hql = "SELECT r.permissions FROM UserGridRole r WHERE r.userId = ? AND r.gridId = ? AND r.status = 1";
        List<String> permissionJsonList = find(hql, userId, gridId);
        
        Set<String> permissions = new HashSet<>();
        for (String permissionJson : permissionJsonList) {
            if (StringUtils.isNotBlank(permissionJson)) {
                // 这里应该解析JSON，简化处理
                String[] perms = permissionJson.replace("[", "").replace("]", "").replace("\"", "").split(",");
                for (String perm : perms) {
                    if (StringUtils.isNotBlank(perm.trim())) {
                        permissions.add(perm.trim());
                    }
                }
            }
        }
        
        return new ArrayList<>(permissions);
    }

    @Override
    public List<UserGridRole> findGridAdmins(String gridId) {
        String hql = "FROM UserGridRole r WHERE r.gridId = ? AND r.roleCode = 'GRID_ADMIN' AND r.status = 1";
        return find(hql, gridId);
    }

    @Override
    public List<UserGridRole> findGridManagers(String gridId) {
        String hql = "FROM UserGridRole r WHERE r.gridId = ? AND r.roleCode = 'GRID_MANAGER' AND r.status = 1";
        return find(hql, gridId);
    }

    @Override
    public List<UserGridRole> findDataCollectors(String gridId) {
        String hql = "FROM UserGridRole r WHERE r.gridId = ? AND r.roleCode = 'DATA_COLLECTOR' AND r.status = 1";
        return find(hql, gridId);
    }

    @Override
    public boolean hasPermission(String userId, String gridId, String permission) {
        List<String> permissions = findUserPermissions(userId, gridId);
        return permissions.contains(permission);
    }

    @Override
    public boolean hasRole(String userId, String gridId, String roleCode) {
        UserGridRole role = findByUserIdAndGridIdAndRoleCode(userId, gridId, roleCode);
        return role != null;
    }

    @Override
    public int deleteUserGridRoles(String userId, String gridId) {
        String hql = "DELETE FROM UserGridRole r WHERE r.userId = ? AND r.gridId = ?";
        Query query = getSession().createQuery(hql);
        query.setParameter(0, userId);
        query.setParameter(1, gridId);
        return query.executeUpdate();
    }

    @Override
    public int deleteUserRole(String userId, String gridId, String roleCode) {
        String hql = "DELETE FROM UserGridRole r WHERE r.userId = ? AND r.gridId = ? AND r.roleCode = ?";
        Query query = getSession().createQuery(hql);
        query.setParameter(0, userId);
        query.setParameter(1, gridId);
        query.setParameter(2, roleCode);
        return query.executeUpdate();
    }

    @Override
    public int batchAssignRoles(List<String> userIds, String gridId, String roleCode, String creatorId) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (String userId : userIds) {
            // 检查是否已存在
            UserGridRole existing = findByUserIdAndGridIdAndRoleCode(userId, gridId, roleCode);
            if (existing == null) {
                UserGridRole role = new UserGridRole();
                role.setUserId(userId);
                role.setGridId(gridId);
                role.setRoleCode(roleCode);
                role.setRoleName(getRoleNameByCode(roleCode));
                role.setCreatorId(creatorId);
                role.setAssignDate(new Date());
                role.setCreateDate(new Date());
                save(role);
                count++;
            }
        }
        
        return count;
    }

    @Override
    public int updateRoleStatus(String id, Integer status, String updaterId) {
        String hql = "UPDATE UserGridRole r SET r.status = ?, r.updateDate = CURRENT_TIMESTAMP, r.updaterId = ? WHERE r.id = ?";
        Query query = getSession().createQuery(hql);
        query.setParameter(0, status);
        query.setParameter(1, updaterId);
        query.setParameter(2, id);
        return query.executeUpdate();
    }

    private String getRoleNameByCode(String roleCode) {
        switch (roleCode) {
            case "GRID_ADMIN":
                return "网格管理员";
            case "GRID_MANAGER":
                return "网格负责人";
            case "DATA_COLLECTOR":
                return "数据采集员";
            default:
                return roleCode;
        }
    }
}
