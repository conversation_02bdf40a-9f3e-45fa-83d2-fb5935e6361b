package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.CityTaskDao;
import net.diaowen.dwsurvey.entity.CityTask;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 城市任务数据访问实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Repository
public class CityTaskDaoImpl extends BaseDaoImpl<CityTask, String> implements CityTaskDao {

    @Override
    public Page<CityTask> findTasksByCondition(Page<CityTask> page, Map<String, Object> params) {
        StringBuilder hql = new StringBuilder("FROM CityTask t WHERE 1=1");
        List<Object> paramList = new ArrayList<>();

        int paramIndex = 1;

        if (params.get("status") != null) {
            hql.append(" AND t.status = ?").append(paramIndex++);
            paramList.add(params.get("status"));
        }

        if (params.get("priority") != null) {
            hql.append(" AND t.priority = ?").append(paramIndex++);
            paramList.add(params.get("priority"));
        }

        if (params.get("taskType") != null) {
            hql.append(" AND t.taskType = ?").append(paramIndex++);
            paramList.add(params.get("taskType"));
        }

        if (params.get("gridId") != null) {
            hql.append(" AND t.gridId = ?").append(paramIndex++);
            paramList.add(params.get("gridId"));
        }

        if (params.get("assigneeId") != null) {
            hql.append(" AND t.assigneeId = ?").append(paramIndex++);
            paramList.add(params.get("assigneeId"));
        }

        if (params.get("creatorId") != null) {
            hql.append(" AND t.creatorId = ?").append(paramIndex++);
            paramList.add(params.get("creatorId"));
        }

        if (params.get("isUrgent") != null) {
            hql.append(" AND t.isUrgent = ?").append(paramIndex++);
            paramList.add(params.get("isUrgent"));
        }

        if (params.get("keyword") != null && StringUtils.isNotBlank((String) params.get("keyword"))) {
            hql.append(" AND (t.title LIKE ?").append(paramIndex++).append(" OR t.description LIKE ?").append(paramIndex++).append(" OR t.taskCode LIKE ?").append(paramIndex++).append(")");
            String keyword = "%" + params.get("keyword") + "%";
            paramList.add(keyword);
            paramList.add(keyword);
            paramList.add(keyword);
        }

        if (params.get("startDate") != null) {
            hql.append(" AND t.createDate >= ?").append(paramIndex++);
            paramList.add(params.get("startDate"));
        }

        if (params.get("endDate") != null) {
            hql.append(" AND t.createDate <= ?").append(paramIndex++);
            paramList.add(params.get("endDate"));
        }

        hql.append(" ORDER BY t.priority DESC, t.createDate DESC");

        return findPage(page, hql.toString(), paramList.toArray());
    }

    @Override
    public List<CityTask> findByGridId(String gridId) {
        String hql = "FROM CityTask t WHERE t.gridId = ?1 ORDER BY t.priority DESC, t.createDate DESC";
        return find(hql, gridId);
    }

    @Override
    public List<CityTask> findByAssigneeId(String assigneeId) {
        String hql = "FROM CityTask t WHERE t.assigneeId = ?1 ORDER BY t.priority DESC, t.createDate DESC";
        return find(hql, assigneeId);
    }

    @Override
    public List<CityTask> findByStatus(String status) {
        String hql = "FROM CityTask t WHERE t.status = ?1 ORDER BY t.priority DESC, t.createDate DESC";
        return find(hql, status);
    }

    @Override
    public List<CityTask> findByPriority(Integer priority) {
        String hql = "FROM CityTask t WHERE t.priority = ?1 ORDER BY t.createDate DESC";
        return find(hql, priority);
    }

    @Override
    public List<CityTask> findUrgentTasks() {
        String hql = "FROM CityTask t WHERE t.isUrgent = true AND t.status NOT IN ('COMPLETED', 'CANCELLED') ORDER BY t.createDate DESC";
        return find(hql);
    }

    @Override
    public List<CityTask> findOverdueTasks() {
        String hql = "FROM CityTask t WHERE t.deadline < CURRENT_TIMESTAMP AND t.status NOT IN ('COMPLETED', 'CANCELLED') ORDER BY t.deadline ASC";
        return find(hql);
    }

    @Override
    public CityTask findByTaskCode(String taskCode) {
        String hql = "FROM CityTask t WHERE t.taskCode = ?1";
        return findUnique(hql, taskCode);
    }

    @Override
    public Map<String, Long> countTasksByAssignee(String assigneeId) {
        String hql = "SELECT t.status, COUNT(t) FROM CityTask t WHERE t.assigneeId = ?1 GROUP BY t.status";
        List<Object[]> results = find(hql, assigneeId);

        Map<String, Long> counts = new HashMap<>();
        for (Object[] result : results) {
            counts.put((String) result[0], (Long) result[1]);
        }
        return counts;
    }

    @Override
    public Map<String, Long> countTasksByGrid(String gridId) {
        String hql = "SELECT t.status, COUNT(t) FROM CityTask t WHERE t.gridId = ?1 GROUP BY t.status";
        List<Object[]> results = find(hql, gridId);

        Map<String, Long> counts = new HashMap<>();
        for (Object[] result : results) {
            counts.put((String) result[0], (Long) result[1]);
        }
        return counts;
    }

    @Override
    public int updateTaskStatus(String taskId, String status, String updaterId) {
        String hql = "UPDATE CityTask t SET t.status = ?1, t.updateDate = CURRENT_TIMESTAMP, t.updaterId = ?2 WHERE t.id = ?3";
        Query query = getSession().createQuery(hql);
        query.setParameter(1, status);
        query.setParameter(2, updaterId);
        query.setParameter(3, taskId);
        return query.executeUpdate();
    }

    @Override
    public int batchAssignTasks(List<String> taskIds, String assigneeId, String updaterId) {
        if (taskIds == null || taskIds.isEmpty()) {
            return 0;
        }
        
        String hql = "UPDATE CityTask t SET t.assigneeId = ?1, t.status = 'ASSIGNED', t.updateDate = CURRENT_TIMESTAMP, t.updaterId = ?2 WHERE t.id IN (:taskIds)";
        Query query = getSession().createQuery(hql);
        query.setParameter(1, assigneeId);
        query.setParameter(2, updaterId);
        query.setParameterList("taskIds", taskIds);
        return query.executeUpdate();
    }
}
