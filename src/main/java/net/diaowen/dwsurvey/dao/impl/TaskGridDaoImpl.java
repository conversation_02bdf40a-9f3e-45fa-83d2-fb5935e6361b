package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.TaskGridDao;
import net.diaowen.dwsurvey.entity.TaskGrid;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 网格区域数据访问实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Repository
public class TaskGridDaoImpl extends BaseDaoImpl<TaskGrid, String> implements TaskGridDao {

    @Override
    public Page<TaskGrid> findGridsByCondition(Page<TaskGrid> page, Map<String, Object> params) {
        StringBuilder hql = new StringBuilder("FROM TaskGrid g WHERE g.status = 1");
        List<Object> paramList = new ArrayList<>();

        if (params.get("parentId") != null) {
            hql.append(" AND g.parentId = ?");
            paramList.add(params.get("parentId"));
        }

        if (params.get("gridType") != null) {
            hql.append(" AND g.gridType = ?");
            paramList.add(params.get("gridType"));
        }

        if (params.get("gridLevel") != null) {
            hql.append(" AND g.gridLevel = ?");
            paramList.add(params.get("gridLevel"));
        }

        if (params.get("keyword") != null && StringUtils.isNotBlank((String) params.get("keyword"))) {
            hql.append(" AND (g.gridName LIKE ? OR g.gridCode LIKE ? OR g.address LIKE ?)");
            String keyword = "%" + params.get("keyword") + "%";
            paramList.add(keyword);
            paramList.add(keyword);
            paramList.add(keyword);
        }

        hql.append(" ORDER BY g.gridLevel ASC, g.createDate ASC");

        return findPage(page, hql.toString(), paramList.toArray());
    }

    @Override
    public List<TaskGrid> findByParentId(String parentId) {
        String hql = "FROM TaskGrid g WHERE g.parentId = ? AND g.status = 1 ORDER BY g.gridLevel ASC, g.createDate ASC";
        return find(hql, parentId);
    }

    @Override
    public List<TaskGrid> findByGridType(String gridType) {
        String hql = "FROM TaskGrid g WHERE g.gridType = ? AND g.status = 1 ORDER BY g.gridLevel ASC, g.createDate ASC";
        return find(hql, gridType);
    }

    @Override
    public List<TaskGrid> findByGridLevel(Integer gridLevel) {
        String hql = "FROM TaskGrid g WHERE g.gridLevel = ? AND g.status = 1 ORDER BY g.createDate ASC";
        return find(hql, gridLevel);
    }

    @Override
    public TaskGrid findByGridCode(String gridCode) {
        String hql = "FROM TaskGrid g WHERE g.gridCode = ? AND g.status = 1";
        return findUnique(hql, gridCode);
    }

    @Override
    public List<TaskGrid> findRootGrids() {
        String hql = "FROM TaskGrid g WHERE g.parentId IS NULL AND g.status = 1 ORDER BY g.createDate ASC";
        return find(hql);
    }

    @Override
    public List<Map<String, Object>> buildGridTree() {
        List<TaskGrid> allGrids = findAll();
        Map<String, List<TaskGrid>> gridMap = new HashMap<>();
        List<TaskGrid> rootGrids = new ArrayList<>();

        // 分组网格
        for (TaskGrid grid : allGrids) {
            if (grid.getParentId() == null) {
                rootGrids.add(grid);
            } else {
                gridMap.computeIfAbsent(grid.getParentId(), k -> new ArrayList<>()).add(grid);
            }
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (TaskGrid rootGrid : rootGrids) {
            result.add(buildGridNode(rootGrid, gridMap));
        }

        return result;
    }

    private Map<String, Object> buildGridNode(TaskGrid grid, Map<String, List<TaskGrid>> gridMap) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", grid.getId());
        node.put("gridCode", grid.getGridCode());
        node.put("gridName", grid.getGridName());
        node.put("gridType", grid.getGridType());
        node.put("gridLevel", grid.getGridLevel());
        node.put("parentId", grid.getParentId());

        List<TaskGrid> children = gridMap.get(grid.getId());
        if (children != null && !children.isEmpty()) {
            List<Map<String, Object>> childNodes = new ArrayList<>();
            for (TaskGrid child : children) {
                childNodes.add(buildGridNode(child, gridMap));
            }
            node.put("children", childNodes);
        }

        return node;
    }

    @Override
    public TaskGrid findByAreaCode(String areaCode) {
        String hql = "FROM TaskGrid g WHERE g.areaCode = ? AND g.status = 1";
        return findUnique(hql, areaCode);
    }

    @Override
    public List<TaskGrid> findAllChildGrids(String gridId) {
        String hql = "WITH RECURSIVE grid_tree AS (" +
                    "  SELECT * FROM t_task_grid WHERE id = ? " +
                    "  UNION ALL " +
                    "  SELECT g.* FROM t_task_grid g " +
                    "  INNER JOIN grid_tree gt ON g.parent_id = gt.id " +
                    ") " +
                    "SELECT * FROM grid_tree WHERE id != ?";
        
        // 由于Hibernate不直接支持递归查询，这里使用简化版本
        List<TaskGrid> result = new ArrayList<>();
        List<TaskGrid> directChildren = findByParentId(gridId);
        result.addAll(directChildren);
        
        for (TaskGrid child : directChildren) {
            result.addAll(findAllChildGrids(child.getId()));
        }
        
        return result;
    }

    @Override
    public List<TaskGrid> findGridPath(String gridId) {
        List<TaskGrid> path = new ArrayList<>();
        TaskGrid current = get(gridId);
        
        while (current != null) {
            path.add(0, current); // 添加到开头
            if (current.getParentId() != null) {
                current = get(current.getParentId());
            } else {
                break;
            }
        }
        
        return path;
    }

    @Override
    public List<TaskGrid> findByLocation(Double longitude, Double latitude) {
        // 简化实现，实际应该使用空间查询
        String hql = "FROM TaskGrid g WHERE g.longitude IS NOT NULL AND g.latitude IS NOT NULL AND g.status = 1";
        return find(hql);
    }

    @Override
    public Long countTasksByGrid(String gridId) {
        String hql = "SELECT COUNT(t) FROM CityTask t WHERE t.gridId = ?";
        return (Long) findUnique(hql, gridId);
    }

    @Override
    public int updateGridStatus(String gridId, Integer status, String updaterId) {
        String hql = "UPDATE TaskGrid g SET g.status = ?, g.updateDate = CURRENT_TIMESTAMP, g.updaterId = ? WHERE g.id = ?";
        Query query = getSession().createQuery(hql);
        query.setParameter(0, status);
        query.setParameter(1, updaterId);
        query.setParameter(2, gridId);
        return query.executeUpdate();
    }
}
