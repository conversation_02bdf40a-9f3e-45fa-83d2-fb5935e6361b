package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.CityTask;

import java.util.List;
import java.util.Map;

/**
 * 城市任务数据访问接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface CityTaskDao extends BaseDao<CityTask, String> {

    /**
     * 根据条件分页查询任务列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<CityTask> findTasksByCondition(Page<CityTask> page, Map<String, Object> params);

    /**
     * 根据网格ID查询任务列表
     * @param gridId 网格ID
     * @return 任务列表
     */
    List<CityTask> findByGridId(String gridId);

    /**
     * 根据负责人ID查询任务列表
     * @param assigneeId 负责人ID
     * @return 任务列表
     */
    List<CityTask> findByAssigneeId(String assigneeId);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<CityTask> findByStatus(String status);

    /**
     * 根据优先级查询任务列表
     * @param priority 优先级
     * @return 任务列表
     */
    List<CityTask> findByPriority(Integer priority);

    /**
     * 查询紧急任务列表
     * @return 紧急任务列表
     */
    List<CityTask> findUrgentTasks();

    /**
     * 查询逾期任务列表
     * @return 逾期任务列表
     */
    List<CityTask> findOverdueTasks();

    /**
     * 根据任务编码查询任务
     * @param taskCode 任务编码
     * @return 任务对象
     */
    CityTask findByTaskCode(String taskCode);

    /**
     * 统计用户任务数量
     * @param assigneeId 负责人ID
     * @return 任务数量统计
     */
    Map<String, Long> countTasksByAssignee(String assigneeId);

    /**
     * 统计网格任务数量
     * @param gridId 网格ID
     * @return 任务数量统计
     */
    Map<String, Long> countTasksByGrid(String gridId);

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新结果
     */
    int updateTaskStatus(String taskId, String status, String updaterId);

    /**
     * 批量分配任务
     * @param taskIds 任务ID列表
     * @param assigneeId 负责人ID
     * @param updaterId 更新人ID
     * @return 更新数量
     */
    int batchAssignTasks(List<String> taskIds, String assigneeId, String updaterId);
}
