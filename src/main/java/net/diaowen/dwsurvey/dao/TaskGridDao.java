package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.TaskGrid;

import java.util.List;
import java.util.Map;

/**
 * 网格区域数据访问接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface TaskGridDao extends BaseDao<TaskGrid, String> {

    /**
     * 根据条件分页查询网格列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<TaskGrid> findGridsByCondition(Page<TaskGrid> page, Map<String, Object> params);

    /**
     * 根据父网格ID查询子网格列表
     * @param parentId 父网格ID
     * @return 子网格列表
     */
    List<TaskGrid> findByParentId(String parentId);

    /**
     * 根据网格类型查询网格列表
     * @param gridType 网格类型
     * @return 网格列表
     */
    List<TaskGrid> findByGridType(String gridType);

    /**
     * 根据网格层级查询网格列表
     * @param gridLevel 网格层级
     * @return 网格列表
     */
    List<TaskGrid> findByGridLevel(Integer gridLevel);

    /**
     * 根据网格编码查询网格
     * @param gridCode 网格编码
     * @return 网格对象
     */
    TaskGrid findByGridCode(String gridCode);

    /**
     * 查询根网格列表（顶级网格）
     * @return 根网格列表
     */
    List<TaskGrid> findRootGrids();

    /**
     * 构建网格树形结构
     * @return 网格树形结构
     */
    List<Map<String, Object>> buildGridTree();

    /**
     * 根据区域代码查询网格
     * @param areaCode 区域代码
     * @return 网格对象
     */
    TaskGrid findByAreaCode(String areaCode);

    /**
     * 查询指定网格的所有子网格（递归）
     * @param gridId 网格ID
     * @return 子网格列表
     */
    List<TaskGrid> findAllChildGrids(String gridId);

    /**
     * 查询指定网格的路径（从根到当前网格）
     * @param gridId 网格ID
     * @return 网格路径
     */
    List<TaskGrid> findGridPath(String gridId);

    /**
     * 根据坐标查询网格
     * @param longitude 经度
     * @param latitude 纬度
     * @return 网格列表
     */
    List<TaskGrid> findByLocation(Double longitude, Double latitude);

    /**
     * 统计网格下的任务数量
     * @param gridId 网格ID
     * @return 任务数量
     */
    Long countTasksByGrid(String gridId);

    /**
     * 更新网格状态
     * @param gridId 网格ID
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 更新结果
     */
    int updateGridStatus(String gridId, Integer status, String updaterId);
}
