package net.diaowen.dwsurvey.controller.task;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.UserGridRole;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户网格角色管理控制器
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Api(tags = "用户网格角色管理")
@RestController
@RequestMapping("/api/dwsurvey/app/user-grid-role")
public class UserGridRoleController {

    @Autowired
    private UserGridRoleManager userGridRoleManager;

    @Autowired
    private AccountManager accountManager;

    @ApiOperation("获取用户角色列表")
    @GetMapping("/list")
    public HttpResult<Page<UserGridRole>> getRoleList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("用户ID") @RequestParam(required = false) String userId,
            @ApiParam("网格ID") @RequestParam(required = false) String gridId,
            @ApiParam("角色代码") @RequestParam(required = false) String roleCode,
            @ApiParam("关键词") @RequestParam(required = false) String keyword) {
        
        try {
            Page<UserGridRole> pageObj = new Page<>();
            pageObj.setPageNo(page);
            pageObj.setPageSize(pageSize);

            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(userId)) {
                params.put("userId", userId);
            }
            if (StringUtils.isNotBlank(gridId)) {
                params.put("gridId", gridId);
            }
            if (StringUtils.isNotBlank(roleCode)) {
                params.put("roleCode", roleCode);
            }
            if (StringUtils.isNotBlank(keyword)) {
                params.put("keyword", keyword);
            }

            Page<UserGridRole> result = userGridRoleManager.findRolesByCondition(pageObj, params);
            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取用户角色列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户角色详情")
    @GetMapping("/{roleId}")
    public HttpResult<UserGridRole> getRoleDetail(@ApiParam("角色ID") @PathVariable String roleId) {
        try {
            if (StringUtils.isBlank(roleId)) {
                return HttpResult.FAILURE_MSG("角色ID不能为空");
            }

            UserGridRole role = userGridRoleManager.get(roleId);
            if (role == null) {
                return HttpResult.FAILURE_MSG("角色不存在");
            }

            return HttpResult.SUCCESS(role);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取角色详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("分配用户到网格角色")
    @PostMapping("/assign")
    public HttpResult<UserGridRole> assignUserToGrid(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("角色代码") @RequestParam String roleCode,
            @ApiParam("权限列表") @RequestParam(required = false) List<String> permissions) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
                return HttpResult.FAILURE_MSG("用户ID、网格ID和角色代码不能为空");
            }

            // 设置默认权限
            if (permissions == null || permissions.isEmpty()) {
                permissions = getDefaultPermissionsByRole(roleCode);
            }

            UserGridRole role = userGridRoleManager.assignUserToGrid(userId, gridId, roleCode, permissions, currentUser.getId());
            return HttpResult.SUCCESS(role);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("分配用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("批量分配用户到网格角色")
    @PostMapping("/batch-assign")
    public HttpResult<String> batchAssignUsersToGrid(
            @ApiParam("用户ID列表") @RequestParam List<String> userIds,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("角色代码") @RequestParam String roleCode,
            @ApiParam("权限列表") @RequestParam(required = false) List<String> permissions) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (userIds == null || userIds.isEmpty() || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
                return HttpResult.FAILURE_MSG("用户ID列表、网格ID和角色代码不能为空");
            }

            // 设置默认权限
            if (permissions == null || permissions.isEmpty()) {
                permissions = getDefaultPermissionsByRole(roleCode);
            }

            int count = userGridRoleManager.batchAssignUsersToGrid(userIds, gridId, roleCode, permissions, currentUser.getId());
            return HttpResult.SUCCESS("成功分配 " + count + " 个用户角色");
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("批量分配用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("移除用户网格角色")
    @PostMapping("/remove")
    public HttpResult<String> removeUserFromGrid(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("角色代码") @RequestParam String roleCode) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
                return HttpResult.FAILURE_MSG("用户ID、网格ID和角色代码不能为空");
            }

            boolean success = userGridRoleManager.removeUserFromGrid(userId, gridId, roleCode, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("移除用户角色成功");
            } else {
                return HttpResult.FAILURE_MSG("移除用户角色失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("移除用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("移除用户在网格的所有角色")
    @PostMapping("/remove-all")
    public HttpResult<String> removeUserFromGridAllRoles(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("用户ID和网格ID不能为空");
            }

            int count = userGridRoleManager.removeUserFromGridAllRoles(userId, gridId, currentUser.getId());
            return HttpResult.SUCCESS("成功移除 " + count + " 个用户角色");
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("移除用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新用户权限")
    @PostMapping("/update-permissions")
    public HttpResult<String> updateUserPermissions(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("角色代码") @RequestParam String roleCode,
            @ApiParam("权限列表") @RequestParam List<String> permissions) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
                return HttpResult.FAILURE_MSG("用户ID、网格ID和角色代码不能为空");
            }

            boolean success = userGridRoleManager.updateUserPermissions(userId, gridId, roleCode, permissions, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("更新用户权限成功");
            } else {
                return HttpResult.FAILURE_MSG("更新用户权限失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("更新用户权限失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户角色")
    @GetMapping("/user/{userId}")
    public HttpResult<List<UserGridRole>> getUserRoles(@ApiParam("用户ID") @PathVariable String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return HttpResult.FAILURE_MSG("用户ID不能为空");
            }

            List<UserGridRole> roles = userGridRoleManager.findRolesByUserId(userId);
            return HttpResult.SUCCESS(roles);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取网格角色")
    @GetMapping("/grid/{gridId}")
    public HttpResult<List<UserGridRole>> getGridRoles(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            List<UserGridRole> roles = userGridRoleManager.findRolesByGridId(gridId);
            return HttpResult.SUCCESS(roles);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户权限")
    @GetMapping("/permissions")
    public HttpResult<List<String>> getUserPermissions(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId) {
        
        try {
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("用户ID和网格ID不能为空");
            }

            List<String> permissions = userGridRoleManager.findUserPermissions(userId, gridId);
            return HttpResult.SUCCESS(permissions);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取用户权限失败：" + e.getMessage());
        }
    }

    @ApiOperation("检查用户权限")
    @GetMapping("/check-permission")
    public HttpResult<Boolean> checkUserPermission(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("权限代码") @RequestParam String permission) {
        
        try {
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(permission)) {
                return HttpResult.FAILURE_MSG("参数不能为空");
            }

            boolean hasPermission = userGridRoleManager.hasPermission(userId, gridId, permission);
            return HttpResult.SUCCESS(hasPermission);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("检查用户权限失败：" + e.getMessage());
        }
    }

    @ApiOperation("检查用户角色")
    @GetMapping("/check-role")
    public HttpResult<Boolean> checkUserRole(
            @ApiParam("用户ID") @RequestParam String userId,
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("角色代码") @RequestParam String roleCode) {
        
        try {
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
                return HttpResult.FAILURE_MSG("参数不能为空");
            }

            boolean hasRole = userGridRoleManager.hasRole(userId, gridId, roleCode);
            return HttpResult.SUCCESS(hasRole);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("检查用户角色失败：" + e.getMessage());
        }
    }

    @ApiOperation("转移网格负责人")
    @PostMapping("/transfer-manager")
    public HttpResult<String> transferGridManager(
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("原负责人ID") @RequestParam String fromUserId,
            @ApiParam("新负责人ID") @RequestParam String toUserId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(gridId) || StringUtils.isBlank(fromUserId) || StringUtils.isBlank(toUserId)) {
                return HttpResult.FAILURE_MSG("参数不能为空");
            }

            boolean success = userGridRoleManager.transferGridManager(gridId, fromUserId, toUserId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("转移网格负责人成功");
            } else {
                return HttpResult.FAILURE_MSG("转移网格负责人失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("转移网格负责人失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户可管理的网格")
    @GetMapping("/manageable-grids/{userId}")
    public HttpResult<List<String>> getManageableGrids(@ApiParam("用户ID") @PathVariable String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return HttpResult.FAILURE_MSG("用户ID不能为空");
            }

            List<String> gridIds = userGridRoleManager.findManageableGrids(userId);
            return HttpResult.SUCCESS(gridIds);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取可管理网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户可访问的网格")
    @GetMapping("/accessible-grids/{userId}")
    public HttpResult<List<String>> getAccessibleGrids(@ApiParam("用户ID") @PathVariable String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return HttpResult.FAILURE_MSG("用户ID不能为空");
            }

            List<String> gridIds = userGridRoleManager.findAccessibleGrids(userId);
            return HttpResult.SUCCESS(gridIds);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取可访问网格失败：" + e.getMessage());
        }
    }

    /**
     * 根据角色获取默认权限
     */
    private List<String> getDefaultPermissionsByRole(String roleCode) {
        switch (roleCode) {
            case "GRID_ADMIN":
                return Arrays.asList("VIEW", "CREATE", "UPDATE", "DELETE", "ASSIGN", "MANAGE_USERS");
            case "GRID_MANAGER":
                return Arrays.asList("VIEW", "CREATE", "UPDATE", "ASSIGN");
            case "DATA_COLLECTOR":
                return Arrays.asList("VIEW", "UPDATE");
            default:
                return Arrays.asList("VIEW");
        }
    }
}
