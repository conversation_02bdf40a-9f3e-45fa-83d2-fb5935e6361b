package net.diaowen.dwsurvey.controller.task;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.service.TaskGridManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网格管理控制器
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Api(tags = "网格管理")
@RestController
@RequestMapping("/api/task-grid")
public class TaskGridController {

    @Autowired
    private TaskGridManager taskGridManager;

    @Autowired
    private AccountManager accountManager;

    @ApiOperation("获取网格列表")
    @GetMapping("/list")
    public HttpResult<Page<TaskGrid>> getGridList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("父网格ID") @RequestParam(required = false) String parentId,
            @ApiParam("网格类型") @RequestParam(required = false) String gridType,
            @ApiParam("网格层级") @RequestParam(required = false) Integer gridLevel,
            @ApiParam("关键词") @RequestParam(required = false) String keyword) {
        
        try {
            Page<TaskGrid> pageObj = new Page<>();
            pageObj.setPageNo(page);
            pageObj.setPageSize(pageSize);

            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(parentId)) {
                params.put("parentId", parentId);
            }
            if (StringUtils.isNotBlank(gridType)) {
                params.put("gridType", gridType);
            }
            if (gridLevel != null) {
                params.put("gridLevel", gridLevel);
            }
            if (StringUtils.isNotBlank(keyword)) {
                params.put("keyword", keyword);
            }

            Page<TaskGrid> result = taskGridManager.findGridsByCondition(pageObj, params);
            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取网格详情")
    @GetMapping("/{gridId}")
    public HttpResult<TaskGrid> getGridDetail(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            TaskGrid grid = taskGridManager.get(gridId);
            if (grid == null) {
                return HttpResult.FAILURE_MSG("网格不存在");
            }

            return HttpResult.SUCCESS(grid);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建网格")
    @PostMapping("/create")
    public HttpResult<TaskGrid> createGrid(@RequestBody TaskGrid grid) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (grid == null) {
                return HttpResult.FAILURE_MSG("网格信息不能为空");
            }

            if (StringUtils.isBlank(grid.getGridName())) {
                return HttpResult.FAILURE_MSG("网格名称不能为空");
            }

            if (StringUtils.isBlank(grid.getGridType())) {
                return HttpResult.FAILURE_MSG("网格类型不能为空");
            }

            TaskGrid createdGrid = taskGridManager.createGrid(grid, currentUser.getId());
            return HttpResult.SUCCESS(createdGrid);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("创建网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新网格")
    @PostMapping("/update")
    public HttpResult<TaskGrid> updateGrid(@RequestBody TaskGrid grid) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (grid == null || StringUtils.isBlank(grid.getId())) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            // 检查权限
            if (!taskGridManager.checkGridPermission(grid.getId(), currentUser.getId(), "UPDATE")) {
                return HttpResult.FAILURE_MSG("没有权限更新此网格");
            }

            TaskGrid updatedGrid = taskGridManager.updateGrid(grid, currentUser.getId());
            return HttpResult.SUCCESS(updatedGrid);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("更新网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除网格")
    @PostMapping("/delete/{gridId}")
    public HttpResult<String> deleteGrid(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            // 检查权限
            if (!taskGridManager.checkGridPermission(gridId, currentUser.getId(), "DELETE")) {
                return HttpResult.FAILURE_MSG("没有权限删除此网格");
            }

            boolean success = taskGridManager.deleteGrid(gridId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("网格删除成功");
            } else {
                return HttpResult.FAILURE_MSG("网格删除失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("删除网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取网格树")
    @GetMapping("/tree")
    public HttpResult<List<Map<String, Object>>> getGridTree() {
        try {
            List<Map<String, Object>> tree = taskGridManager.buildGridTree();
            return HttpResult.SUCCESS(tree);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格树失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取子网格列表")
    @GetMapping("/children/{parentId}")
    public HttpResult<List<TaskGrid>> getChildGrids(@ApiParam("父网格ID") @PathVariable String parentId) {
        try {
            if (StringUtils.isBlank(parentId)) {
                return HttpResult.FAILURE_MSG("父网格ID不能为空");
            }

            List<TaskGrid> childGrids = taskGridManager.findChildGrids(parentId);
            return HttpResult.SUCCESS(childGrids);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取子网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取根网格列表")
    @GetMapping("/roots")
    public HttpResult<List<TaskGrid>> getRootGrids() {
        try {
            List<TaskGrid> rootGrids = taskGridManager.findRootGrids();
            return HttpResult.SUCCESS(rootGrids);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取根网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据网格类型获取网格列表")
    @GetMapping("/by-type/{gridType}")
    public HttpResult<List<TaskGrid>> getGridsByType(@ApiParam("网格类型") @PathVariable String gridType) {
        try {
            if (StringUtils.isBlank(gridType)) {
                return HttpResult.FAILURE_MSG("网格类型不能为空");
            }

            List<TaskGrid> grids = taskGridManager.findGridsByType(gridType);
            return HttpResult.SUCCESS(grids);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据网格层级获取网格列表")
    @GetMapping("/by-level/{gridLevel}")
    public HttpResult<List<TaskGrid>> getGridsByLevel(@ApiParam("网格层级") @PathVariable Integer gridLevel) {
        try {
            if (gridLevel == null) {
                return HttpResult.FAILURE_MSG("网格层级不能为空");
            }

            List<TaskGrid> grids = taskGridManager.findGridsByLevel(gridLevel);
            return HttpResult.SUCCESS(grids);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据网格编码获取网格")
    @GetMapping("/by-code/{gridCode}")
    public HttpResult<TaskGrid> getGridByCode(@ApiParam("网格编码") @PathVariable String gridCode) {
        try {
            if (StringUtils.isBlank(gridCode)) {
                return HttpResult.FAILURE_MSG("网格编码不能为空");
            }

            TaskGrid grid = taskGridManager.findByGridCode(gridCode);
            if (grid == null) {
                return HttpResult.FAILURE_MSG("网格不存在");
            }

            return HttpResult.SUCCESS(grid);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取网格路径")
    @GetMapping("/path/{gridId}")
    public HttpResult<List<TaskGrid>> getGridPath(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            List<TaskGrid> path = taskGridManager.findGridPath(gridId);
            return HttpResult.SUCCESS(path);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格路径失败：" + e.getMessage());
        }
    }

    @ApiOperation("启用网格")
    @PostMapping("/enable/{gridId}")
    public HttpResult<String> enableGrid(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            boolean success = taskGridManager.enableGrid(gridId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("网格启用成功");
            } else {
                return HttpResult.FAILURE_MSG("网格启用失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("启用网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("禁用网格")
    @PostMapping("/disable/{gridId}")
    public HttpResult<String> disableGrid(@ApiParam("网格ID") @PathVariable String gridId) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            boolean success = taskGridManager.disableGrid(gridId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("网格禁用成功");
            } else {
                return HttpResult.FAILURE_MSG("网格禁用失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("禁用网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("移动网格")
    @PostMapping("/move")
    public HttpResult<String> moveGrid(
            @ApiParam("网格ID") @RequestParam String gridId,
            @ApiParam("新父网格ID") @RequestParam(required = false) String newParentId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(gridId)) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            boolean success = taskGridManager.moveGrid(gridId, newParentId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("网格移动成功");
            } else {
                return HttpResult.FAILURE_MSG("网格移动失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("移动网格失败：" + e.getMessage());
        }
    }

    @ApiOperation("复制网格")
    @PostMapping("/copy")
    public HttpResult<TaskGrid> copyGrid(
            @ApiParam("源网格ID") @RequestParam String sourceGridId,
            @ApiParam("目标父网格ID") @RequestParam(required = false) String targetParentId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(sourceGridId)) {
                return HttpResult.FAILURE_MSG("源网格ID不能为空");
            }

            TaskGrid copiedGrid = taskGridManager.copyGrid(sourceGridId, targetParentId, currentUser.getId());
            if (copiedGrid != null) {
                return HttpResult.SUCCESS(copiedGrid);
            } else {
                return HttpResult.FAILURE_MSG("网格复制失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("复制网格失败：" + e.getMessage());
        }
    }
}
