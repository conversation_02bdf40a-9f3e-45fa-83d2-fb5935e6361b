package net.diaowen.dwsurvey.controller.task;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.service.CityTaskManager;
import net.diaowen.dwsurvey.service.TaskGridManager;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import net.diaowen.dwsurvey.service.TaskDataInitService;
import net.diaowen.common.base.service.UserManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市任务管理控制器
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Api(tags = "城市任务管理")
@RestController
@RequestMapping("/api/dwsurvey/app/city-task")
public class CityTaskController {

    @Autowired
    private CityTaskManager cityTaskManager;

    @Autowired
    private AccountManager accountManager;

    @Autowired
    private TaskDataInitService taskDataInitService;

    @Autowired
    private TaskGridManager taskGridManager;

    @Autowired
    private UserGridRoleManager userGridRoleManager;

    @Autowired
    private UserManager userManager;

    @ApiOperation("获取任务列表")
    @GetMapping("/list.do")
    public HttpResult<Page<CityTask>> getTaskList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("任务状态") @RequestParam(required = false) String status,
            @ApiParam("优先级") @RequestParam(required = false) Integer priority,
            @ApiParam("任务类型") @RequestParam(required = false) String taskType,
            @ApiParam("网格ID") @RequestParam(required = false) String gridId,
            @ApiParam("负责人ID") @RequestParam(required = false) String assigneeId,
            @ApiParam("关键词") @RequestParam(required = false) String keyword) {
        
        try {
            Page<CityTask> pageObj = new Page<>();
            pageObj.setPageNo(page);
            pageObj.setPageSize(pageSize);

            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(status)) {
                params.put("status", status);
            }
            if (priority != null) {
                params.put("priority", priority);
            }
            if (StringUtils.isNotBlank(taskType)) {
                params.put("taskType", taskType);
            }
            if (StringUtils.isNotBlank(gridId)) {
                params.put("gridId", gridId);
            }
            if (StringUtils.isNotBlank(assigneeId)) {
                params.put("assigneeId", assigneeId);
            }
            if (StringUtils.isNotBlank(keyword)) {
                params.put("keyword", keyword);
            }

            Page<CityTask> result = cityTaskManager.findTasksByCondition(pageObj, params);
            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务详情")
    @GetMapping("/{taskId}")
    public HttpResult<CityTask> getTaskDetail(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            CityTask task = cityTaskManager.get(taskId);
            if (task == null) {
                return HttpResult.FAILURE_MSG("任务不存在");
            }

            return HttpResult.SUCCESS(task);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建任务")
    @PostMapping("/create.do")
    public HttpResult<CityTask> createTask(@RequestBody CityTask task) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (task == null) {
                return HttpResult.FAILURE_MSG("任务信息不能为空");
            }

            if (StringUtils.isBlank(task.getTitle())) {
                return HttpResult.FAILURE_MSG("任务标题不能为空");
            }

            if (StringUtils.isBlank(task.getGridId())) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            if (StringUtils.isBlank(task.getTaskType())) {
                return HttpResult.FAILURE_MSG("任务类型不能为空");
            }

            // 验证网格权限
            if (!userGridRoleManager.hasPermission(currentUser.getId(), task.getGridId(), "CREATE")) {
                return HttpResult.FAILURE_MSG("没有权限在此网格创建任务");
            }

            // 验证负责人权限（如果指定了负责人）
            if (StringUtils.isNotBlank(task.getAssigneeId())) {
                if (!userGridRoleManager.hasPermission(task.getAssigneeId(), task.getGridId(), "VIEW")) {
                    return HttpResult.FAILURE_MSG("指定的负责人没有访问此网格的权限");
                }
            }

            // 设置默认值
            if (task.getPriority() == null) {
                task.setPriority(2); // 默认中等优先级
            }

            if (task.getEstimatedDuration() == null) {
                task.setEstimatedDuration(120); // 默认2小时
            }

            CityTask createdTask = cityTaskManager.createTask(task, currentUser.getId());
            return HttpResult.SUCCESS(createdTask);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("创建任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新任务")
    @PostMapping("/update")
    public HttpResult<CityTask> updateTask(@RequestBody CityTask task) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (task == null || StringUtils.isBlank(task.getId())) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(task.getId(), currentUser.getId(), "UPDATE")) {
                return HttpResult.FAILURE_MSG("没有权限更新此任务");
            }

            CityTask updatedTask = cityTaskManager.updateTask(task, currentUser.getId());
            return HttpResult.SUCCESS(updatedTask);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("更新任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("分配任务")
    @PostMapping("/assign")
    public HttpResult<String> assignTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("负责人ID") @RequestParam String assigneeId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId) || StringUtils.isBlank(assigneeId)) {
                return HttpResult.FAILURE_MSG("任务ID和负责人ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(taskId, currentUser.getId(), "ASSIGN")) {
                return HttpResult.FAILURE_MSG("没有权限分配此任务");
            }

            boolean success = cityTaskManager.assignTask(taskId, assigneeId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("任务分配成功");
            } else {
                return HttpResult.FAILURE_MSG("任务分配失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("分配任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("批量分配任务")
    @PostMapping("/batch-assign")
    public HttpResult<String> batchAssignTasks(
            @ApiParam("任务ID列表") @RequestParam List<String> taskIds,
            @ApiParam("负责人ID") @RequestParam String assigneeId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (taskIds == null || taskIds.isEmpty() || StringUtils.isBlank(assigneeId)) {
                return HttpResult.FAILURE_MSG("任务ID列表和负责人ID不能为空");
            }

            int count = cityTaskManager.batchAssignTasks(taskIds, assigneeId, currentUser.getId());
            return HttpResult.SUCCESS("成功分配 " + count + " 个任务");
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("批量分配任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("开始任务")
    @PostMapping("/start")
    public HttpResult<String> startTask(@ApiParam("任务ID") @RequestParam String taskId) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            boolean success = cityTaskManager.startTask(taskId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("任务开始成功");
            } else {
                return HttpResult.FAILURE_MSG("任务开始失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("开始任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("完成任务")
    @PostMapping("/complete")
    public HttpResult<String> completeTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("结果数据") @RequestParam(required = false) String resultData) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            boolean success = cityTaskManager.completeTask(taskId, currentUser.getId(), resultData);
            if (success) {
                return HttpResult.SUCCESS("任务完成成功");
            } else {
                return HttpResult.FAILURE_MSG("任务完成失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("完成任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("取消任务")
    @PostMapping("/cancel")
    public HttpResult<String> cancelTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("取消原因") @RequestParam(required = false) String reason) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(taskId, currentUser.getId(), "CANCEL")) {
                return HttpResult.FAILURE_MSG("没有权限取消此任务");
            }

            boolean success = cityTaskManager.cancelTask(taskId, currentUser.getId(), reason);
            if (success) {
                return HttpResult.SUCCESS("任务取消成功");
            } else {
                return HttpResult.FAILURE_MSG("任务取消失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("取消任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取我的待办任务")
    @GetMapping("/my-pending")
    public HttpResult<List<CityTask>> getMyPendingTasks() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            List<CityTask> tasks = cityTaskManager.findPendingTasksByUser(currentUser.getId());
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取待办任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取紧急任务")
    @GetMapping("/urgent")
    public HttpResult<List<CityTask>> getUrgentTasks() {
        try {
            List<CityTask> tasks = cityTaskManager.findUrgentTasks();
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取紧急任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取逾期任务")
    @GetMapping("/overdue")
    public HttpResult<List<CityTask>> getOverdueTasks() {
        try {
            List<CityTask> tasks = cityTaskManager.findOverdueTasks();
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取逾期任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("统计任务数量")
    @GetMapping("/statistics")
    public HttpResult<Map<String, Object>> getTaskStatistics(
            @ApiParam("网格ID") @RequestParam(required = false) String gridId,
            @ApiParam("负责人ID") @RequestParam(required = false) String assigneeId) {
        
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            if (StringUtils.isNotBlank(gridId)) {
                Map<String, Long> gridStats = cityTaskManager.countTasksByGrid(gridId);
                statistics.put("gridStatistics", gridStats);
            }
            
            if (StringUtils.isNotBlank(assigneeId)) {
                Map<String, Long> userStats = cityTaskManager.countTasksByAssignee(assigneeId);
                statistics.put("userStatistics", userStats);
            }

            return HttpResult.SUCCESS(statistics);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务统计失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取推送通知列表")
    @GetMapping("/push-list.do")
    public HttpResult<List<Map<String, Object>>> getPushList() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            // 模拟推送通知数据
            List<Map<String, Object>> pushList = new ArrayList<>();

            // 获取用户的待办任务作为推送通知
            List<CityTask> pendingTasks = cityTaskManager.findPendingTasksByUser(currentUser.getId());
            for (CityTask task : pendingTasks) {
                Map<String, Object> notification = new HashMap<>();
                notification.put("id", task.getId());
                notification.put("title", "新任务分配");
                notification.put("content", "您有新的任务：" + task.getTitle());
                notification.put("type", "TASK_ASSIGNMENT");
                notification.put("taskId", task.getId());
                notification.put("priority", task.getPriority());
                notification.put("createTime", task.getCreateDate());
                notification.put("isRead", false);
                pushList.add(notification);
            }

            // 获取紧急任务作为推送通知
            List<CityTask> urgentTasks = cityTaskManager.findUrgentTasks();
            for (CityTask task : urgentTasks) {
                Map<String, Object> notification = new HashMap<>();
                notification.put("id", "urgent_" + task.getId());
                notification.put("title", "紧急任务");
                notification.put("content", "紧急任务需要处理：" + task.getTitle());
                notification.put("type", "URGENT_TASK");
                notification.put("taskId", task.getId());
                notification.put("priority", task.getPriority());
                notification.put("createTime", task.getCreateDate());
                notification.put("isRead", false);
                pushList.add(notification);
            }

            return HttpResult.SUCCESS(pushList);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取推送通知列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务类型列表")
    @GetMapping("/types.do")
    public HttpResult<List<Map<String, Object>>> getTaskTypes() {
        try {
            List<Map<String, Object>> taskTypes = new ArrayList<>();

            Map<String, Object> survey = new HashMap<>();
            survey.put("code", "SURVEY");
            survey.put("name", "问卷调查");
            survey.put("description", "通过问卷形式收集数据");
            survey.put("icon", "survey");
            taskTypes.add(survey);

            Map<String, Object> inspection = new HashMap<>();
            inspection.put("code", "INSPECTION");
            inspection.put("name", "现场检查");
            inspection.put("description", "实地检查和评估");
            inspection.put("icon", "inspection");
            taskTypes.add(inspection);

            Map<String, Object> dataCollection = new HashMap<>();
            dataCollection.put("code", "DATA_COLLECTION");
            dataCollection.put("name", "数据采集");
            dataCollection.put("description", "收集和整理数据");
            dataCollection.put("icon", "data");
            taskTypes.add(dataCollection);

            Map<String, Object> monitoring = new HashMap<>();
            monitoring.put("code", "MONITORING");
            monitoring.put("name", "监测任务");
            monitoring.put("description", "持续监测和观察");
            monitoring.put("icon", "monitoring");
            taskTypes.add(monitoring);

            return HttpResult.SUCCESS(taskTypes);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务类型失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务详情")
    @GetMapping("/info.do")
    public HttpResult<CityTask> getTaskInfo(@ApiParam("任务ID") @RequestParam String id) {
        try {
            if (StringUtils.isBlank(id)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            CityTask task = cityTaskManager.get(id);
            if (task == null) {
                return HttpResult.FAILURE_MSG("任务不存在");
            }

            return HttpResult.SUCCESS(task);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新任务状态")
    @PostMapping("/update-status.do")
    public HttpResult<String> updateTaskStatus(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("新状态") @RequestParam String status) {

        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId) || StringUtils.isBlank(status)) {
                return HttpResult.FAILURE_MSG("任务ID和状态不能为空");
            }

            boolean success = cityTaskManager.updateTaskStatus(taskId, status, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("任务状态更新成功");
            } else {
                return HttpResult.FAILURE_MSG("任务状态更新失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("更新任务状态失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务统计数据")
    @GetMapping("/stats.do")
    public HttpResult<Map<String, Object>> getTaskStats() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            Map<String, Object> stats = new HashMap<>();

            // 获取用户任务统计
            Map<String, Long> userStats = cityTaskManager.countTasksByAssignee(currentUser.getId());
            stats.put("userStats", userStats);

            // 获取总体统计
            Map<String, Object> totalStats = new HashMap<>();
            totalStats.put("totalTasks", userStats.values().stream().mapToLong(Long::longValue).sum());
            totalStats.put("pendingTasks", userStats.getOrDefault("PENDING", 0L));
            totalStats.put("inProgressTasks", userStats.getOrDefault("IN_PROGRESS", 0L));
            totalStats.put("completedTasks", userStats.getOrDefault("COMPLETED", 0L));
            totalStats.put("urgentTasks", cityTaskManager.findUrgentTasks().size());
            totalStats.put("overdueTasks", cityTaskManager.findOverdueTasks().size());

            stats.put("totalStats", totalStats);

            return HttpResult.SUCCESS(stats);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务统计失败：" + e.getMessage());
        }
    }

    @ApiOperation("搜索任务")
    @GetMapping("/search.do")
    public HttpResult<List<CityTask>> searchTasks(
            @ApiParam("搜索关键词") @RequestParam String keyword,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(keyword)) {
                return HttpResult.FAILURE_MSG("搜索关键词不能为空");
            }

            Page<CityTask> pageObj = new Page<>();
            pageObj.setPageNo(page);
            pageObj.setPageSize(pageSize);

            Map<String, Object> params = new HashMap<>();
            params.put("keyword", keyword);
            params.put("assigneeId", currentUser.getId()); // 只搜索当前用户的任务

            Page<CityTask> result = cityTaskManager.findTasksByCondition(pageObj, params);
            return HttpResult.SUCCESS(result.getResult());
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("搜索任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("初始化测试数据")
    @PostMapping("/init-data.do")
    public HttpResult<String> initTestData() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            taskDataInitService.initTestData(currentUser.getId());
            return HttpResult.SUCCESS("测试数据初始化成功");
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("初始化测试数据失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取网格列表（用于任务创建）")
    @GetMapping("/grids.do")
    public HttpResult<List<Map<String, Object>>> getGridsForTask() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            // 获取用户可访问的网格
            List<String> accessibleGridIds = userGridRoleManager.findAccessibleGrids(currentUser.getId());
            List<Map<String, Object>> grids = new ArrayList<>();

            for (String gridId : accessibleGridIds) {
                TaskGrid grid = taskGridManager.get(gridId);
                if (grid != null && grid.getStatus() == 1) {
                    Map<String, Object> gridInfo = new HashMap<>();
                    gridInfo.put("id", grid.getId());
                    gridInfo.put("gridCode", grid.getGridCode());
                    gridInfo.put("gridName", grid.getGridName());
                    gridInfo.put("gridType", grid.getGridType());
                    gridInfo.put("gridLevel", grid.getGridLevel());
                    gridInfo.put("parentId", grid.getParentId());
                    gridInfo.put("address", grid.getAddress());
                    grids.add(gridInfo);
                }
            }

            return HttpResult.SUCCESS(grids);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取网格列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取用户列表（用于任务分配）")
    @GetMapping("/users.do")
    public HttpResult<List<Map<String, Object>>> getUsersForTask(
            @ApiParam("网格ID") @RequestParam(required = false) String gridId) {

        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            List<Map<String, Object>> users = new ArrayList<>();

            if (StringUtils.isNotBlank(gridId)) {
                // 获取指定网格的用户
                List<net.diaowen.dwsurvey.entity.UserGridRole> roles = userGridRoleManager.findRolesByGridId(gridId);
                Set<String> userIds = new HashSet<>();

                for (net.diaowen.dwsurvey.entity.UserGridRole role : roles) {
                    userIds.add(role.getUserId());
                }

                for (String userId : userIds) {
                    User user = userManager.get(userId);
                    if (user != null) {
                        Map<String, Object> userInfo = new HashMap<>();
                        userInfo.put("id", user.getId());
                        userInfo.put("loginName", user.getLoginName());
                        userInfo.put("name", user.getName());
                        userInfo.put("email", user.getEmail());
                        userInfo.put("cellphone", user.getCellphone());
                        users.add(userInfo);
                    }
                }
            } else {
                // 获取当前用户可管理的网格中的所有用户
                List<String> manageableGridIds = userGridRoleManager.findManageableGrids(currentUser.getId());
                Set<String> userIds = new HashSet<>();

                for (String managedGridId : manageableGridIds) {
                    List<net.diaowen.dwsurvey.entity.UserGridRole> roles = userGridRoleManager.findRolesByGridId(managedGridId);
                    for (net.diaowen.dwsurvey.entity.UserGridRole role : roles) {
                        userIds.add(role.getUserId());
                    }
                }

                for (String userId : userIds) {
                    User user = userManager.get(userId);
                    if (user != null) {
                        Map<String, Object> userInfo = new HashMap<>();
                        userInfo.put("id", user.getId());
                        userInfo.put("loginName", user.getLoginName());
                        userInfo.put("name", user.getName());
                        userInfo.put("email", user.getEmail());
                        userInfo.put("cellphone", user.getCellphone());
                        users.add(userInfo);
                    }
                }
            }

            return HttpResult.SUCCESS(users);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取用户列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务创建表单配置")
    @GetMapping("/form-config.do")
    public HttpResult<Map<String, Object>> getTaskFormConfig() {
        try {
            Map<String, Object> config = new HashMap<>();

            // 任务类型配置
            List<Map<String, Object>> taskTypes = new ArrayList<>();
            taskTypes.add(createOption("SURVEY", "问卷调查", "通过问卷形式收集数据"));
            taskTypes.add(createOption("INSPECTION", "现场检查", "实地检查和评估"));
            taskTypes.add(createOption("DATA_COLLECTION", "数据采集", "收集和整理数据"));
            taskTypes.add(createOption("MONITORING", "监测任务", "持续监测和观察"));
            config.put("taskTypes", taskTypes);

            // 优先级配置
            List<Map<String, Object>> priorities = new ArrayList<>();
            priorities.add(createOption(1, "低", "一般任务"));
            priorities.add(createOption(2, "中", "重要任务"));
            priorities.add(createOption(3, "高", "紧急任务"));
            priorities.add(createOption(4, "极高", "特急任务"));
            config.put("priorities", priorities);

            // 任务分类配置
            List<Map<String, Object>> categories = new ArrayList<>();
            categories.add(createOption("民生调查", "民生调查", "涉及民生相关的调查任务"));
            categories.add(createOption("环境检查", "环境检查", "环境卫生检查任务"));
            categories.add(createOption("统计调查", "统计调查", "统计数据收集任务"));
            categories.add(createOption("安全检查", "安全检查", "安全隐患排查任务"));
            categories.add(createOption("基础设施", "基础设施", "基础设施检查任务"));
            config.put("categories", categories);

            return HttpResult.SUCCESS(config);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取表单配置失败：" + e.getMessage());
        }
    }

    private Map<String, Object> createOption(Object value, String label, String description) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        option.put("description", description);
        return option;
    }
}
