package net.diaowen.dwsurvey.controller.task;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.common.plugs.httpclient.HttpResult;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.service.CityTaskManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市任务管理控制器
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Api(tags = "城市任务管理")
@RestController
@RequestMapping("/api/city-task")
public class CityTaskController {

    @Autowired
    private CityTaskManager cityTaskManager;

    @Autowired
    private AccountManager accountManager;

    @ApiOperation("获取任务列表")
    @GetMapping("/list")
    public HttpResult<Page<CityTask>> getTaskList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("任务状态") @RequestParam(required = false) String status,
            @ApiParam("优先级") @RequestParam(required = false) Integer priority,
            @ApiParam("任务类型") @RequestParam(required = false) String taskType,
            @ApiParam("网格ID") @RequestParam(required = false) String gridId,
            @ApiParam("负责人ID") @RequestParam(required = false) String assigneeId,
            @ApiParam("关键词") @RequestParam(required = false) String keyword) {
        
        try {
            Page<CityTask> pageObj = new Page<>();
            pageObj.setPageNo(page);
            pageObj.setPageSize(pageSize);

            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(status)) {
                params.put("status", status);
            }
            if (priority != null) {
                params.put("priority", priority);
            }
            if (StringUtils.isNotBlank(taskType)) {
                params.put("taskType", taskType);
            }
            if (StringUtils.isNotBlank(gridId)) {
                params.put("gridId", gridId);
            }
            if (StringUtils.isNotBlank(assigneeId)) {
                params.put("assigneeId", assigneeId);
            }
            if (StringUtils.isNotBlank(keyword)) {
                params.put("keyword", keyword);
            }

            Page<CityTask> result = cityTaskManager.findTasksByCondition(pageObj, params);
            return HttpResult.SUCCESS(result);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务列表失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取任务详情")
    @GetMapping("/{taskId}")
    public HttpResult<CityTask> getTaskDetail(@ApiParam("任务ID") @PathVariable String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            CityTask task = cityTaskManager.get(taskId);
            if (task == null) {
                return HttpResult.FAILURE_MSG("任务不存在");
            }

            return HttpResult.SUCCESS(task);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务详情失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建任务")
    @PostMapping("/create")
    public HttpResult<CityTask> createTask(@RequestBody CityTask task) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (task == null) {
                return HttpResult.FAILURE_MSG("任务信息不能为空");
            }

            if (StringUtils.isBlank(task.getTitle())) {
                return HttpResult.FAILURE_MSG("任务标题不能为空");
            }

            if (StringUtils.isBlank(task.getGridId())) {
                return HttpResult.FAILURE_MSG("网格ID不能为空");
            }

            CityTask createdTask = cityTaskManager.createTask(task, currentUser.getId());
            return HttpResult.SUCCESS(createdTask);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("创建任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新任务")
    @PostMapping("/update")
    public HttpResult<CityTask> updateTask(@RequestBody CityTask task) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (task == null || StringUtils.isBlank(task.getId())) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(task.getId(), currentUser.getId(), "UPDATE")) {
                return HttpResult.FAILURE_MSG("没有权限更新此任务");
            }

            CityTask updatedTask = cityTaskManager.updateTask(task, currentUser.getId());
            return HttpResult.SUCCESS(updatedTask);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("更新任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("分配任务")
    @PostMapping("/assign")
    public HttpResult<String> assignTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("负责人ID") @RequestParam String assigneeId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId) || StringUtils.isBlank(assigneeId)) {
                return HttpResult.FAILURE_MSG("任务ID和负责人ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(taskId, currentUser.getId(), "ASSIGN")) {
                return HttpResult.FAILURE_MSG("没有权限分配此任务");
            }

            boolean success = cityTaskManager.assignTask(taskId, assigneeId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("任务分配成功");
            } else {
                return HttpResult.FAILURE_MSG("任务分配失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("分配任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("批量分配任务")
    @PostMapping("/batch-assign")
    public HttpResult<String> batchAssignTasks(
            @ApiParam("任务ID列表") @RequestParam List<String> taskIds,
            @ApiParam("负责人ID") @RequestParam String assigneeId) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (taskIds == null || taskIds.isEmpty() || StringUtils.isBlank(assigneeId)) {
                return HttpResult.FAILURE_MSG("任务ID列表和负责人ID不能为空");
            }

            int count = cityTaskManager.batchAssignTasks(taskIds, assigneeId, currentUser.getId());
            return HttpResult.SUCCESS("成功分配 " + count + " 个任务");
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("批量分配任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("开始任务")
    @PostMapping("/start")
    public HttpResult<String> startTask(@ApiParam("任务ID") @RequestParam String taskId) {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            boolean success = cityTaskManager.startTask(taskId, currentUser.getId());
            if (success) {
                return HttpResult.SUCCESS("任务开始成功");
            } else {
                return HttpResult.FAILURE_MSG("任务开始失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("开始任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("完成任务")
    @PostMapping("/complete")
    public HttpResult<String> completeTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("结果数据") @RequestParam(required = false) String resultData) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            boolean success = cityTaskManager.completeTask(taskId, currentUser.getId(), resultData);
            if (success) {
                return HttpResult.SUCCESS("任务完成成功");
            } else {
                return HttpResult.FAILURE_MSG("任务完成失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("完成任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("取消任务")
    @PostMapping("/cancel")
    public HttpResult<String> cancelTask(
            @ApiParam("任务ID") @RequestParam String taskId,
            @ApiParam("取消原因") @RequestParam(required = false) String reason) {
        
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            if (StringUtils.isBlank(taskId)) {
                return HttpResult.FAILURE_MSG("任务ID不能为空");
            }

            // 检查权限
            if (!cityTaskManager.checkTaskPermission(taskId, currentUser.getId(), "CANCEL")) {
                return HttpResult.FAILURE_MSG("没有权限取消此任务");
            }

            boolean success = cityTaskManager.cancelTask(taskId, currentUser.getId(), reason);
            if (success) {
                return HttpResult.SUCCESS("任务取消成功");
            } else {
                return HttpResult.FAILURE_MSG("任务取消失败");
            }
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("取消任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取我的待办任务")
    @GetMapping("/my-pending")
    public HttpResult<List<CityTask>> getMyPendingTasks() {
        try {
            User currentUser = accountManager.getCurUser();
            if (currentUser == null) {
                return HttpResult.NOLOGIN();
            }

            List<CityTask> tasks = cityTaskManager.findPendingTasksByUser(currentUser.getId());
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取待办任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取紧急任务")
    @GetMapping("/urgent")
    public HttpResult<List<CityTask>> getUrgentTasks() {
        try {
            List<CityTask> tasks = cityTaskManager.findUrgentTasks();
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取紧急任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取逾期任务")
    @GetMapping("/overdue")
    public HttpResult<List<CityTask>> getOverdueTasks() {
        try {
            List<CityTask> tasks = cityTaskManager.findOverdueTasks();
            return HttpResult.SUCCESS(tasks);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取逾期任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("统计任务数量")
    @GetMapping("/statistics")
    public HttpResult<Map<String, Object>> getTaskStatistics(
            @ApiParam("网格ID") @RequestParam(required = false) String gridId,
            @ApiParam("负责人ID") @RequestParam(required = false) String assigneeId) {
        
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            if (StringUtils.isNotBlank(gridId)) {
                Map<String, Long> gridStats = cityTaskManager.countTasksByGrid(gridId);
                statistics.put("gridStatistics", gridStats);
            }
            
            if (StringUtils.isNotBlank(assigneeId)) {
                Map<String, Long> userStats = cityTaskManager.countTasksByAssignee(assigneeId);
                statistics.put("userStatistics", userStats);
            }

            return HttpResult.SUCCESS(statistics);
        } catch (Exception e) {
            return HttpResult.FAILURE_MSG("获取任务统计失败：" + e.getMessage());
        }
    }
}
