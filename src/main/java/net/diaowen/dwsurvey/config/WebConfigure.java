package net.diaowen.dwsurvey.config;

import net.diaowen.common.plugs.filter.DwWebResourceFilter;
import net.diaowen.common.plugs.json.jackson.core.spring.JsonReturnHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter;
import org.springframework.web.filter.DelegatingFilterProxy;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.tuckey.web.filters.urlrewrite.UrlRewriteFilter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class WebConfigure implements WebMvcConfigurer {

    /*
    @Bean(name="sitemesh3")
    SiteMeshFilter siteMeshFilter(){
        return new SiteMeshFilter();
    }

    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/").setViewName("forward:/login.do");
        registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
    }
    */

    @Bean
    public FilterRegistrationBean registerOpenEntityManagerInViewFilterBean() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        OpenEntityManagerInViewFilter filter = new OpenEntityManagerInViewFilter();
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("*.do");
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean delegatingFilterProxy(){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistrationBean.setFilter(proxy);
        filterRegistrationBean.addUrlPatterns("/*");
        return filterRegistrationBean;
    }


    /*
    @Bean
    public FilterRegistrationBean filterRegistrationBean(@Qualifier("sitemesh3") SiteMeshFilter siteMeshFilter){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(siteMeshFilter);
        filterRegistrationBean.setEnabled(true);
        filterRegistrationBean.addUrlPatterns("*.do");
        return filterRegistrationBean;
    }


    @Bean
    public FilterRegistrationBean filterRegistrationBean(){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        UrlRewriteFilter urlRewriteFilter = new UrlRewriteFilter();
        filterRegistrationBean.setFilter(urlRewriteFilter);
        filterRegistrationBean.setEnabled(true);
        filterRegistrationBean.addUrlPatterns("/*");
        return filterRegistrationBean;
    }
    */

    @Bean
    public FilterRegistrationBean registerWebFilterBean() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        DwWebResourceFilter filter = new DwWebResourceFilter();
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/webin/*");
        return registrationBean;
    }

    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //文件磁盘图片url 映射
        //配置server虚拟路径，handler为前台访问的目录，locations为files相对应的本地路径
        //registry.addResourceHandler("/WEB-INF/**").addResourceLocations("file:/home/<USER>/dwsurvey/src/main/webapp/WEB-INF");
//        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
    }


    @Override
    public void addReturnValueHandlers(List<HandlerMethodReturnValueHandler> handlers) {
        handlers.add(new JsonReturnHandler());
        WebMvcConfigurer.super.addReturnValueHandlers(handlers);
    }


}
