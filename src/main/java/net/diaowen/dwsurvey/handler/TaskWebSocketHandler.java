package net.diaowen.dwsurvey.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务WebSocket处理器
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Component
public class TaskWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(TaskWebSocketHandler.class);

    // 存储用户会话
    private static final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    
    // 存储网格订阅关系
    private static final Map<String, String> userGridSubscriptions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = getUserIdFromSession(session);
        if (userId != null) {
            userSessions.put(userId, session);
            logger.info("用户 {} 建立WebSocket连接", userId);
            
            // 发送连接成功消息
            sendMessage(session, createMessage("CONNECTION_SUCCESS", "连接成功", null));
        } else {
            logger.warn("无法获取用户ID，关闭连接");
            session.close();
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String userId = getUserIdFromSession(session);
        if (userId == null) {
            return;
        }

        try {
            String payload = message.getPayload().toString();
            JSONObject messageObj = JSON.parseObject(payload);
            String type = messageObj.getString("type");

            switch (type) {
                case "SUBSCRIBE_GRID":
                    handleGridSubscription(userId, messageObj.getString("gridId"));
                    break;
                case "UNSUBSCRIBE_GRID":
                    handleGridUnsubscription(userId);
                    break;
                case "HEARTBEAT":
                    handleHeartbeat(session);
                    break;
                default:
                    logger.warn("未知消息类型: {}", type);
            }
        } catch (Exception e) {
            logger.error("处理WebSocket消息失败", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String userId = getUserIdFromSession(session);
        logger.error("用户 {} WebSocket传输错误", userId, exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String userId = getUserIdFromSession(session);
        if (userId != null) {
            userSessions.remove(userId);
            userGridSubscriptions.remove(userId);
            logger.info("用户 {} 断开WebSocket连接", userId);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 从会话中获取用户ID
     */
    private String getUserIdFromSession(WebSocketSession session) {
        // 从查询参数中获取用户ID
        String query = session.getUri().getQuery();
        if (query != null && query.contains("userId=")) {
            String[] params = query.split("&");
            for (String param : params) {
                if (param.startsWith("userId=")) {
                    return param.substring(7);
                }
            }
        }
        return null;
    }

    /**
     * 处理网格订阅
     */
    private void handleGridSubscription(String userId, String gridId) {
        if (gridId != null) {
            userGridSubscriptions.put(userId, gridId);
            logger.info("用户 {} 订阅网格 {}", userId, gridId);
            
            WebSocketSession session = userSessions.get(userId);
            if (session != null) {
                sendMessage(session, createMessage("GRID_SUBSCRIBED", "网格订阅成功", gridId));
            }
        }
    }

    /**
     * 处理网格取消订阅
     */
    private void handleGridUnsubscription(String userId) {
        String gridId = userGridSubscriptions.remove(userId);
        logger.info("用户 {} 取消订阅网格 {}", userId, gridId);
        
        WebSocketSession session = userSessions.get(userId);
        if (session != null) {
            sendMessage(session, createMessage("GRID_UNSUBSCRIBED", "取消网格订阅成功", gridId));
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session) {
        sendMessage(session, createMessage("HEARTBEAT_RESPONSE", "心跳响应", System.currentTimeMillis()));
    }

    /**
     * 发送消息给指定用户
     */
    public void sendMessageToUser(String userId, String type, String content, Object data) {
        WebSocketSession session = userSessions.get(userId);
        if (session != null && session.isOpen()) {
            sendMessage(session, createMessage(type, content, data));
        }
    }

    /**
     * 发送任务通知给网格内的用户
     */
    public void sendTaskNotificationToGrid(String gridId, String type, String taskId, String taskTitle, Integer priority) {
        JSONObject data = new JSONObject();
        data.put("taskId", taskId);
        data.put("taskTitle", taskTitle);
        data.put("priority", priority);
        data.put("gridId", gridId);
        data.put("timestamp", System.currentTimeMillis());

        userGridSubscriptions.entrySet().stream()
                .filter(entry -> gridId.equals(entry.getValue()))
                .forEach(entry -> {
                    String userId = entry.getKey();
                    sendMessageToUser(userId, type, "新任务通知", data);
                });
    }

    /**
     * 发送紧急任务通知
     */
    public void sendUrgentTaskNotification(String taskId, String taskTitle, String gridId) {
        sendTaskNotificationToGrid(gridId, "URGENT_TASK_NOTIFICATION", taskId, taskTitle, 4);
    }

    /**
     * 发送任务状态更新通知
     */
    public void sendTaskStatusUpdate(String taskId, String status, String assigneeId) {
        JSONObject data = new JSONObject();
        data.put("taskId", taskId);
        data.put("status", status);
        data.put("timestamp", System.currentTimeMillis());

        sendMessageToUser(assigneeId, "TASK_STATUS_UPDATE", "任务状态更新", data);
    }

    /**
     * 发送任务分配通知
     */
    public void sendTaskAssignmentNotification(String taskId, String taskTitle, String assigneeId, Integer priority) {
        JSONObject data = new JSONObject();
        data.put("taskId", taskId);
        data.put("taskTitle", taskTitle);
        data.put("priority", priority);
        data.put("timestamp", System.currentTimeMillis());

        sendMessageToUser(assigneeId, "NEW_TASK_ASSIGNMENT", "新任务分配", data);
    }

    /**
     * 发送任务截止提醒
     */
    public void sendTaskDeadlineReminder(String taskId, String taskTitle, String assigneeId, long remainingHours) {
        JSONObject data = new JSONObject();
        data.put("taskId", taskId);
        data.put("taskTitle", taskTitle);
        data.put("remainingHours", remainingHours);
        data.put("timestamp", System.currentTimeMillis());

        sendMessageToUser(assigneeId, "TASK_DEADLINE_REMINDER", "任务截止提醒", data);
    }

    /**
     * 创建消息对象
     */
    private JSONObject createMessage(String type, String message, Object data) {
        JSONObject messageObj = new JSONObject();
        messageObj.put("type", type);
        messageObj.put("message", message);
        messageObj.put("data", data);
        messageObj.put("timestamp", System.currentTimeMillis());
        return messageObj;
    }

    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, JSONObject message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message.toJSONString()));
            }
        } catch (IOException e) {
            logger.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * 获取在线用户列表
     */
    public Map<String, WebSocketSession> getOnlineUsers() {
        return new ConcurrentHashMap<>(userSessions);
    }
}
