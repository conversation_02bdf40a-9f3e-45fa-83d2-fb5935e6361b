package net.diaowen.dwsurvey.service;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseService;
import net.diaowen.dwsurvey.entity.CityTask;

import java.util.List;
import java.util.Map;

/**
 * 城市任务管理服务接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface CityTaskManager extends BaseService<CityTask, String> {

    /**
     * 根据条件分页查询任务列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<CityTask> findTasksByCondition(Page<CityTask> page, Map<String, Object> params);

    /**
     * 创建任务
     * @param task 任务对象
     * @param creatorId 创建人ID
     * @return 创建的任务
     */
    CityTask createTask(CityTask task, String creatorId);

    /**
     * 更新任务
     * @param task 任务对象
     * @param updaterId 更新人ID
     * @return 更新的任务
     */
    CityTask updateTask(CityTask task, String updaterId);

    /**
     * 分配任务
     * @param taskId 任务ID
     * @param assigneeId 负责人ID
     * @param updaterId 更新人ID
     * @return 是否成功
     */
    boolean assignTask(String taskId, String assigneeId, String updaterId);

    /**
     * 批量分配任务
     * @param taskIds 任务ID列表
     * @param assigneeId 负责人ID
     * @param updaterId 更新人ID
     * @return 分配成功的数量
     */
    int batchAssignTasks(List<String> taskIds, String assigneeId, String updaterId);

    /**
     * 开始任务
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @return 是否成功
     */
    boolean startTask(String taskId, String executorId);

    /**
     * 完成任务
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param resultData 结果数据
     * @return 是否成功
     */
    boolean completeTask(String taskId, String executorId, String resultData);

    /**
     * 取消任务
     * @param taskId 任务ID
     * @param operatorId 操作人ID
     * @param reason 取消原因
     * @return 是否成功
     */
    boolean cancelTask(String taskId, String operatorId, String reason);

    /**
     * 根据网格ID查询任务列表
     * @param gridId 网格ID
     * @return 任务列表
     */
    List<CityTask> findTasksByGridId(String gridId);

    /**
     * 根据负责人ID查询任务列表
     * @param assigneeId 负责人ID
     * @return 任务列表
     */
    List<CityTask> findTasksByAssigneeId(String assigneeId);

    /**
     * 查询用户的待办任务
     * @param userId 用户ID
     * @return 待办任务列表
     */
    List<CityTask> findPendingTasksByUser(String userId);

    /**
     * 查询紧急任务列表
     * @return 紧急任务列表
     */
    List<CityTask> findUrgentTasks();

    /**
     * 查询逾期任务列表
     * @return 逾期任务列表
     */
    List<CityTask> findOverdueTasks();

    /**
     * 根据任务编码查询任务
     * @param taskCode 任务编码
     * @return 任务对象
     */
    CityTask findByTaskCode(String taskCode);

    /**
     * 生成任务编码
     * @return 任务编码
     */
    String generateTaskCode();

    /**
     * 统计用户任务数量
     * @param assigneeId 负责人ID
     * @return 任务数量统计
     */
    Map<String, Long> countTasksByAssignee(String assigneeId);

    /**
     * 统计网格任务数量
     * @param gridId 网格ID
     * @return 任务数量统计
     */
    Map<String, Long> countTasksByGrid(String gridId);

    /**
     * 检查任务权限
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param operation 操作类型
     * @return 是否有权限
     */
    boolean checkTaskPermission(String taskId, String userId, String operation);

    /**
     * 更新任务进度
     * @param taskId 任务ID
     * @param progress 进度(0-100)
     * @param updaterId 更新人ID
     * @return 是否成功
     */
    boolean updateTaskProgress(String taskId, Integer progress, String updaterId);

    /**
     * 添加任务附件
     * @param taskId 任务ID
     * @param attachmentInfo 附件信息
     * @param updaterId 更新人ID
     * @return 是否成功
     */
    boolean addTaskAttachment(String taskId, String attachmentInfo, String updaterId);

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @param updaterId 更新人ID
     * @return 是否成功
     */
    boolean updateTaskStatus(String taskId, String status, String updaterId);
}
