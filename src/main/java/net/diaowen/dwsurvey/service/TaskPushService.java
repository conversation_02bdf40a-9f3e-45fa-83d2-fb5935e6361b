package net.diaowen.dwsurvey.service;

import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.handler.TaskWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 任务推送服务
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class TaskPushService {

    private static final Logger logger = LoggerFactory.getLogger(TaskPushService.class);

    @Autowired
    private TaskWebSocketHandler webSocketHandler;

    @Autowired
    private CityTaskManager cityTaskManager;

    /**
     * 推送新任务通知
     * @param task 任务对象
     */
    public void pushNewTaskNotification(CityTask task) {
        try {
            if (task == null) {
                return;
            }

            logger.info("推送新任务通知: taskId={}, title={}", task.getId(), task.getTitle());

            // 如果是紧急任务，发送紧急通知
            if (Boolean.TRUE.equals(task.getIsUrgent())) {
                webSocketHandler.sendUrgentTaskNotification(
                    task.getId(), 
                    task.getTitle(), 
                    task.getGridId()
                );
            } else {
                // 发送普通任务通知到网格
                webSocketHandler.sendTaskNotificationToGrid(
                    task.getGridId(),
                    "NEW_TASK_NOTIFICATION",
                    task.getId(),
                    task.getTitle(),
                    task.getPriority()
                );
            }

        } catch (Exception e) {
            logger.error("推送新任务通知失败", e);
        }
    }

    /**
     * 推送任务分配通知
     * @param task 任务对象
     */
    public void pushTaskAssignmentNotification(CityTask task) {
        try {
            if (task == null || task.getAssigneeId() == null) {
                return;
            }

            logger.info("推送任务分配通知: taskId={}, assigneeId={}", task.getId(), task.getAssigneeId());

            webSocketHandler.sendTaskAssignmentNotification(
                task.getId(),
                task.getTitle(),
                task.getAssigneeId(),
                task.getPriority()
            );

        } catch (Exception e) {
            logger.error("推送任务分配通知失败", e);
        }
    }

    /**
     * 推送任务状态更新通知
     * @param task 任务对象
     */
    public void pushTaskStatusUpdateNotification(CityTask task) {
        try {
            if (task == null) {
                return;
            }

            logger.info("推送任务状态更新通知: taskId={}, status={}", task.getId(), task.getStatus());

            // 通知任务负责人
            if (task.getAssigneeId() != null) {
                webSocketHandler.sendTaskStatusUpdate(
                    task.getId(),
                    task.getStatus(),
                    task.getAssigneeId()
                );
            }

            // 如果任务完成，通知创建者
            if ("COMPLETED".equals(task.getStatus()) && task.getCreatorId() != null 
                && !task.getCreatorId().equals(task.getAssigneeId())) {
                webSocketHandler.sendMessageToUser(
                    task.getCreatorId(),
                    "TASK_COMPLETED",
                    "任务已完成: " + task.getTitle(),
                    task.getId()
                );
            }

        } catch (Exception e) {
            logger.error("推送任务状态更新通知失败", e);
        }
    }

    /**
     * 推送任务截止提醒
     * @param task 任务对象
     * @param remainingHours 剩余小时数
     */
    public void pushTaskDeadlineReminder(CityTask task, long remainingHours) {
        try {
            if (task == null || task.getAssigneeId() == null) {
                return;
            }

            logger.info("推送任务截止提醒: taskId={}, remainingHours={}", task.getId(), remainingHours);

            webSocketHandler.sendTaskDeadlineReminder(
                task.getId(),
                task.getTitle(),
                task.getAssigneeId(),
                remainingHours
            );

        } catch (Exception e) {
            logger.error("推送任务截止提醒失败", e);
        }
    }

    /**
     * 检查并推送即将到期的任务提醒
     */
    public void checkAndPushDeadlineReminders() {
        try {
            logger.info("开始检查即将到期的任务");

            // 查询所有进行中的任务
            List<CityTask> inProgressTasks = cityTaskManager.findByStatus("IN_PROGRESS");
            List<CityTask> assignedTasks = cityTaskManager.findByStatus("ASSIGNED");

            Date now = new Date();
            
            // 检查进行中的任务
            checkTasksForDeadlineReminder(inProgressTasks, now);
            
            // 检查已分配的任务
            checkTasksForDeadlineReminder(assignedTasks, now);

        } catch (Exception e) {
            logger.error("检查任务截止提醒失败", e);
        }
    }

    /**
     * 检查任务列表中需要提醒的任务
     */
    private void checkTasksForDeadlineReminder(List<CityTask> tasks, Date now) {
        for (CityTask task : tasks) {
            if (task.getDeadline() != null && task.getAssigneeId() != null) {
                long diffInMillis = task.getDeadline().getTime() - now.getTime();
                long remainingHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                // 在截止前24小时、6小时、1小时发送提醒
                if (remainingHours == 24 || remainingHours == 6 || remainingHours == 1) {
                    pushTaskDeadlineReminder(task, remainingHours);
                }
                // 如果已经逾期，更新任务状态
                else if (remainingHours < 0 && !"OVERDUE".equals(task.getStatus())) {
                    cityTaskManager.updateTaskStatus(task.getId(), "OVERDUE", "system");
                    pushTaskStatusUpdateNotification(task);
                }
            }
        }
    }

    /**
     * 推送紧急任务通知给所有相关用户
     * @param task 紧急任务
     */
    public void pushUrgentTaskToAllUsers(CityTask task) {
        try {
            if (task == null || !Boolean.TRUE.equals(task.getIsUrgent())) {
                return;
            }

            logger.info("推送紧急任务给所有相关用户: taskId={}", task.getId());

            // 发送给网格内所有用户
            webSocketHandler.sendUrgentTaskNotification(
                task.getId(),
                task.getTitle(),
                task.getGridId()
            );

        } catch (Exception e) {
            logger.error("推送紧急任务通知失败", e);
        }
    }

    /**
     * 推送任务进度更新通知
     * @param task 任务对象
     */
    public void pushTaskProgressUpdate(CityTask task) {
        try {
            if (task == null) {
                return;
            }

            logger.info("推送任务进度更新: taskId={}, progress={}", task.getId(), task.getProgress());

            // 通知任务创建者
            if (task.getCreatorId() != null) {
                webSocketHandler.sendMessageToUser(
                    task.getCreatorId(),
                    "TASK_PROGRESS_UPDATE",
                    "任务进度更新: " + task.getTitle() + " (" + task.getProgress() + "%)",
                    task.getId()
                );
            }

        } catch (Exception e) {
            logger.error("推送任务进度更新失败", e);
        }
    }

    /**
     * 推送系统通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     */
    public void pushSystemNotification(String userId, String title, String content) {
        try {
            logger.info("推送系统通知给用户: userId={}, title={}", userId, title);

            webSocketHandler.sendMessageToUser(
                userId,
                "SYSTEM_NOTIFICATION",
                title,
                content
            );

        } catch (Exception e) {
            logger.error("推送系统通知失败", e);
        }
    }

    /**
     * 推送广播通知给所有在线用户
     * @param title 通知标题
     * @param content 通知内容
     */
    public void pushBroadcastNotification(String title, String content) {
        try {
            logger.info("推送广播通知: title={}", title);

            webSocketHandler.getOnlineUsers().keySet().forEach(userId -> {
                webSocketHandler.sendMessageToUser(
                    userId,
                    "BROADCAST_NOTIFICATION",
                    title,
                    content
                );
            });

        } catch (Exception e) {
            logger.error("推送广播通知失败", e);
        }
    }
}
