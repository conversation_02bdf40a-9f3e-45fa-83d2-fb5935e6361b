package net.diaowen.dwsurvey.service;

import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.entity.UserGridRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

/**
 * 任务数据初始化服务
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class TaskDataInitService {

    private static final Logger logger = LoggerFactory.getLogger(TaskDataInitService.class);

    @Autowired
    private CityTaskManager cityTaskManager;

    @Autowired
    private TaskGridManager taskGridManager;

    @Autowired
    private UserGridRoleManager userGridRoleManager;

    /**
     * 初始化测试数据
     * @param userId 用户ID
     */
    @Transactional
    public void initTestData(String userId) {
        try {
            logger.info("开始初始化任务管理测试数据，用户ID: {}", userId);

            // 1. 创建网格数据
            TaskGrid cityGrid = createCityGrid(userId);
            TaskGrid districtGrid = createDistrictGrid(cityGrid.getId(), userId);
            TaskGrid streetGrid = createStreetGrid(districtGrid.getId(), userId);
            TaskGrid communityGrid = createCommunityGrid(streetGrid.getId(), userId);

            // 2. 分配用户角色
            assignUserRoles(userId, Arrays.asList(cityGrid.getId(), districtGrid.getId(), streetGrid.getId(), communityGrid.getId()));

            // 3. 创建任务数据
            createTestTasks(userId, Arrays.asList(cityGrid.getId(), districtGrid.getId(), streetGrid.getId(), communityGrid.getId()));

            logger.info("任务管理测试数据初始化完成");
        } catch (Exception e) {
            logger.error("初始化测试数据失败", e);
            throw new RuntimeException("初始化测试数据失败", e);
        }
    }

    private TaskGrid createCityGrid(String userId) {
        TaskGrid grid = new TaskGrid();
        grid.setGridCode("CITY_001");
        grid.setGridName("测试城市");
        grid.setGridType("CITY");
        grid.setGridLevel(1);
        grid.setAreaCode("100000");
        grid.setLongitude(new BigDecimal("116.407526"));
        grid.setLatitude(new BigDecimal("39.904030"));
        grid.setAddress("测试城市中心区");
        grid.setContactPerson("城市管理员");
        grid.setContactPhone("13800138001");
        grid.setDescription("测试城市网格，用于演示城市体检任务管理功能");
        
        return taskGridManager.createGrid(grid, userId);
    }

    private TaskGrid createDistrictGrid(String parentId, String userId) {
        TaskGrid grid = new TaskGrid();
        grid.setGridCode("DISTRICT_001");
        grid.setGridName("测试区县");
        grid.setGridType("DISTRICT");
        grid.setGridLevel(2);
        grid.setParentId(parentId);
        grid.setAreaCode("100001");
        grid.setLongitude(new BigDecimal("116.407526"));
        grid.setLatitude(new BigDecimal("39.904030"));
        grid.setAddress("测试区县政府");
        grid.setContactPerson("区县管理员");
        grid.setContactPhone("13800138002");
        grid.setDescription("测试区县网格");
        
        return taskGridManager.createGrid(grid, userId);
    }

    private TaskGrid createStreetGrid(String parentId, String userId) {
        TaskGrid grid = new TaskGrid();
        grid.setGridCode("STREET_001");
        grid.setGridName("测试街道");
        grid.setGridType("STREET");
        grid.setGridLevel(3);
        grid.setParentId(parentId);
        grid.setAreaCode("100002");
        grid.setLongitude(new BigDecimal("116.407526"));
        grid.setLatitude(new BigDecimal("39.904030"));
        grid.setAddress("测试街道办事处");
        grid.setContactPerson("街道管理员");
        grid.setContactPhone("13800138003");
        grid.setDescription("测试街道网格");
        
        return taskGridManager.createGrid(grid, userId);
    }

    private TaskGrid createCommunityGrid(String parentId, String userId) {
        TaskGrid grid = new TaskGrid();
        grid.setGridCode("COMMUNITY_001");
        grid.setGridName("测试社区");
        grid.setGridType("COMMUNITY");
        grid.setGridLevel(4);
        grid.setParentId(parentId);
        grid.setAreaCode("100003");
        grid.setLongitude(new BigDecimal("116.407526"));
        grid.setLatitude(new BigDecimal("39.904030"));
        grid.setAddress("测试社区居委会");
        grid.setContactPerson("社区管理员");
        grid.setContactPhone("13800138004");
        grid.setDescription("测试社区网格");
        
        return taskGridManager.createGrid(grid, userId);
    }

    private void assignUserRoles(String userId, java.util.List<String> gridIds) {
        for (String gridId : gridIds) {
            // 分配网格管理员角色
            userGridRoleManager.assignUserToGrid(
                userId, 
                gridId, 
                "GRID_ADMIN", 
                Arrays.asList("VIEW", "CREATE", "UPDATE", "DELETE", "ASSIGN", "MANAGE_USERS"), 
                userId
            );
        }
    }

    private void createTestTasks(String userId, java.util.List<String> gridIds) {
        // 创建不同类型的测试任务
        createSurveyTask(userId, gridIds.get(3)); // 社区问卷调查
        createInspectionTask(userId, gridIds.get(2)); // 街道现场检查
        createDataCollectionTask(userId, gridIds.get(1)); // 区县数据采集
        createMonitoringTask(userId, gridIds.get(0)); // 城市监测任务
        createUrgentTask(userId, gridIds.get(3)); // 紧急任务
    }

    private void createSurveyTask(String userId, String gridId) {
        CityTask task = new CityTask();
        task.setTitle("社区居民满意度调查");
        task.setDescription("针对社区居民进行满意度调查，了解居民对社区服务的评价和建议");
        task.setTaskType("SURVEY");
        task.setCategory("民生调查");
        task.setPriority(2);
        task.setStatus("PENDING");
        task.setGridId(gridId);
        task.setAssigneeId(userId);
        task.setEstimatedDuration(120);
        task.setLocationAddress("测试社区居委会");
        task.setLocationLongitude(new BigDecimal("116.407526"));
        task.setLocationLatitude(new BigDecimal("39.904030"));
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 7);
        task.setDeadline(cal.getTime());
        
        cityTaskManager.createTask(task, userId);
    }

    private void createInspectionTask(String userId, String gridId) {
        CityTask task = new CityTask();
        task.setTitle("街道环境卫生检查");
        task.setDescription("对街道环境卫生状况进行全面检查，包括垃圾清理、绿化维护等");
        task.setTaskType("INSPECTION");
        task.setCategory("环境检查");
        task.setPriority(3);
        task.setStatus("ASSIGNED");
        task.setGridId(gridId);
        task.setAssigneeId(userId);
        task.setEstimatedDuration(180);
        task.setLocationAddress("测试街道主要路段");
        task.setLocationLongitude(new BigDecimal("116.407526"));
        task.setLocationLatitude(new BigDecimal("39.904030"));
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 3);
        task.setDeadline(cal.getTime());
        
        cityTaskManager.createTask(task, userId);
    }

    private void createDataCollectionTask(String userId, String gridId) {
        CityTask task = new CityTask();
        task.setTitle("区县人口统计数据采集");
        task.setDescription("收集区县最新的人口统计数据，包括常住人口、流动人口等信息");
        task.setTaskType("DATA_COLLECTION");
        task.setCategory("统计调查");
        task.setPriority(3);
        task.setStatus("IN_PROGRESS");
        task.setGridId(gridId);
        task.setAssigneeId(userId);
        task.setEstimatedDuration(240);
        task.setProgress(30);
        task.setLocationAddress("区县统计局");
        task.setLocationLongitude(new BigDecimal("116.407526"));
        task.setLocationLatitude(new BigDecimal("39.904030"));
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 10);
        task.setDeadline(cal.getTime());
        
        cityTaskManager.createTask(task, userId);
    }

    private void createMonitoringTask(String userId, String gridId) {
        CityTask task = new CityTask();
        task.setTitle("城市空气质量监测");
        task.setDescription("持续监测城市空气质量指标，包括PM2.5、PM10、二氧化硫等");
        task.setTaskType("MONITORING");
        task.setCategory("环境监测");
        task.setPriority(4);
        task.setStatus("PENDING");
        task.setGridId(gridId);
        task.setAssigneeId(userId);
        task.setEstimatedDuration(480);
        task.setLocationAddress("城市环境监测站");
        task.setLocationLongitude(new BigDecimal("116.407526"));
        task.setLocationLatitude(new BigDecimal("39.904030"));
        task.setIsRecurring(true);
        task.setRecurringRule("每日监测");
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        task.setDeadline(cal.getTime());
        
        cityTaskManager.createTask(task, userId);
    }

    private void createUrgentTask(String userId, String gridId) {
        CityTask task = new CityTask();
        task.setTitle("紧急安全隐患排查");
        task.setDescription("发现安全隐患，需要立即进行排查和处理");
        task.setTaskType("INSPECTION");
        task.setCategory("安全检查");
        task.setPriority(4);
        task.setStatus("PENDING");
        task.setGridId(gridId);
        task.setAssigneeId(userId);
        task.setEstimatedDuration(60);
        task.setIsUrgent(true);
        task.setLocationAddress("测试社区安全隐患点");
        task.setLocationLongitude(new BigDecimal("116.407526"));
        task.setLocationLatitude(new BigDecimal("39.904030"));
        
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR_OF_DAY, 2);
        task.setDeadline(cal.getTime());
        
        cityTaskManager.createTask(task, userId);
    }
}
