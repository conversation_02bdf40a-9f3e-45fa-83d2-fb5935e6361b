package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseServiceImpl;
import net.diaowen.common.utils.RandomUtils;
import net.diaowen.dwsurvey.dao.TaskGridDao;
import net.diaowen.dwsurvey.entity.TaskGrid;
import net.diaowen.dwsurvey.service.TaskGridManager;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 网格管理服务实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class TaskGridManagerImpl extends BaseServiceImpl<TaskGrid, String> implements TaskGridManager {

    @Autowired
    private TaskGridDao taskGridDao;

    @Autowired
    private UserGridRoleManager userGridRoleManager;

    @Override
    public void setBaseDao() {
        this.baseDao = taskGridDao;
    }

    @Override
    public Page<TaskGrid> findGridsByCondition(Page<TaskGrid> page, Map<String, Object> params) {
        return taskGridDao.findGridsByCondition(page, params);
    }

    @Override
    @Transactional
    public TaskGrid createGrid(TaskGrid grid, String creatorId) {
        if (grid == null) {
            throw new IllegalArgumentException("网格对象不能为空");
        }

        // 设置基本信息
        grid.setCreatorId(creatorId);
        grid.setCreateDate(new Date());

        // 生成网格编码
        if (StringUtils.isBlank(grid.getGridCode())) {
            grid.setGridCode(generateGridCode(grid.getParentId(), grid.getGridType()));
        }

        // 设置网格层级
        if (grid.getGridLevel() == null) {
            if (StringUtils.isBlank(grid.getParentId())) {
                grid.setGridLevel(1); // 根网格
            } else {
                TaskGrid parentGrid = get(grid.getParentId());
                if (parentGrid != null) {
                    grid.setGridLevel(parentGrid.getGridLevel() + 1);
                }
            }
        }

        // 设置默认状态
        if (grid.getStatus() == null) {
            grid.setStatus(1);
        }

        save(grid);
        return grid;
    }

    @Override
    @Transactional
    public TaskGrid updateGrid(TaskGrid grid, String updaterId) {
        if (grid == null || StringUtils.isBlank(grid.getId())) {
            throw new IllegalArgumentException("网格ID不能为空");
        }

        TaskGrid existingGrid = get(grid.getId());
        if (existingGrid == null) {
            throw new IllegalArgumentException("网格不存在");
        }

        // 更新基本信息
        grid.setUpdaterId(updaterId);
        grid.setUpdateDate(new Date());

        save(grid);
        return grid;
    }

    @Override
    @Transactional
    public boolean deleteGrid(String gridId, String operatorId) {
        if (StringUtils.isBlank(gridId)) {
            return false;
        }

        TaskGrid grid = get(gridId);
        if (grid == null) {
            return false;
        }

        // 检查是否有子网格
        List<TaskGrid> childGrids = findChildGrids(gridId);
        if (!childGrids.isEmpty()) {
            throw new IllegalStateException("存在子网格，无法删除");
        }

        // 检查是否有关联任务
        Long taskCount = countTasksByGrid(gridId);
        if (taskCount > 0) {
            throw new IllegalStateException("存在关联任务，无法删除");
        }

        // 软删除：设置状态为禁用
        return disableGrid(gridId, operatorId);
    }

    @Override
    public List<TaskGrid> findChildGrids(String parentId) {
        return taskGridDao.findByParentId(parentId);
    }

    @Override
    public List<TaskGrid> findGridsByType(String gridType) {
        return taskGridDao.findByGridType(gridType);
    }

    @Override
    public List<TaskGrid> findGridsByLevel(Integer gridLevel) {
        return taskGridDao.findByGridLevel(gridLevel);
    }

    @Override
    public TaskGrid findByGridCode(String gridCode) {
        return taskGridDao.findByGridCode(gridCode);
    }

    @Override
    public List<TaskGrid> findRootGrids() {
        return taskGridDao.findRootGrids();
    }

    @Override
    public List<Map<String, Object>> buildGridTree() {
        return taskGridDao.buildGridTree();
    }

    @Override
    public List<Map<String, Object>> buildGridSubTree(String parentId) {
        List<TaskGrid> childGrids = findChildGrids(parentId);
        List<Map<String, Object>> result = new ArrayList<>();

        for (TaskGrid grid : childGrids) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", grid.getId());
            node.put("gridCode", grid.getGridCode());
            node.put("gridName", grid.getGridName());
            node.put("gridType", grid.getGridType());
            node.put("gridLevel", grid.getGridLevel());
            node.put("parentId", grid.getParentId());

            // 递归获取子节点
            List<Map<String, Object>> children = buildGridSubTree(grid.getId());
            if (!children.isEmpty()) {
                node.put("children", children);
            }

            result.add(node);
        }

        return result;
    }

    @Override
    public TaskGrid findByAreaCode(String areaCode) {
        return taskGridDao.findByAreaCode(areaCode);
    }

    @Override
    public List<TaskGrid> findAllChildGrids(String gridId) {
        return taskGridDao.findAllChildGrids(gridId);
    }

    @Override
    public List<TaskGrid> findGridPath(String gridId) {
        return taskGridDao.findGridPath(gridId);
    }

    @Override
    public List<TaskGrid> findGridsByLocation(Double longitude, Double latitude) {
        return taskGridDao.findByLocation(longitude, latitude);
    }

    @Override
    public String generateGridCode(String parentId, String gridType) {
        String prefix = "";
        switch (gridType) {
            case "CITY":
                prefix = "CITY";
                break;
            case "DISTRICT":
                prefix = "DISTRICT";
                break;
            case "STREET":
                prefix = "STREET";
                break;
            case "COMMUNITY":
                prefix = "COMMUNITY";
                break;
            default:
                prefix = "GRID";
        }

        String randomStr = RandomUtils.randomNum(3);
        return prefix + "_" + randomStr;
    }

    @Override
    public boolean isGridCodeExists(String gridCode) {
        TaskGrid grid = findByGridCode(gridCode);
        return grid != null;
    }

    @Override
    public Long countTasksByGrid(String gridId) {
        return taskGridDao.countTasksByGrid(gridId);
    }

    @Override
    public boolean checkGridPermission(String gridId, String userId, String operation) {
        // 检查用户在该网格的权限
        return userGridRoleManager.hasPermission(userId, gridId, operation);
    }

    @Override
    @Transactional
    public boolean enableGrid(String gridId, String operatorId) {
        return taskGridDao.updateGridStatus(gridId, 1, operatorId) > 0;
    }

    @Override
    @Transactional
    public boolean disableGrid(String gridId, String operatorId) {
        return taskGridDao.updateGridStatus(gridId, 0, operatorId) > 0;
    }

    @Override
    @Transactional
    public boolean moveGrid(String gridId, String newParentId, String operatorId) {
        if (StringUtils.isBlank(gridId)) {
            return false;
        }

        TaskGrid grid = get(gridId);
        if (grid == null) {
            return false;
        }

        // 检查新父网格是否存在
        if (StringUtils.isNotBlank(newParentId)) {
            TaskGrid newParent = get(newParentId);
            if (newParent == null) {
                return false;
            }
            // 更新网格层级
            grid.setGridLevel(newParent.getGridLevel() + 1);
        } else {
            grid.setGridLevel(1);
        }

        grid.setParentId(newParentId);
        grid.setUpdaterId(operatorId);
        grid.setUpdateDate(new Date());

        save(grid);
        return true;
    }

    @Override
    @Transactional
    public TaskGrid copyGrid(String sourceGridId, String targetParentId, String operatorId) {
        if (StringUtils.isBlank(sourceGridId)) {
            return null;
        }

        TaskGrid sourceGrid = get(sourceGridId);
        if (sourceGrid == null) {
            return null;
        }

        TaskGrid newGrid = new TaskGrid();
        newGrid.setGridName(sourceGrid.getGridName() + "_副本");
        newGrid.setGridType(sourceGrid.getGridType());
        newGrid.setParentId(targetParentId);
        newGrid.setAreaCode(sourceGrid.getAreaCode());
        newGrid.setLongitude(sourceGrid.getLongitude());
        newGrid.setLatitude(sourceGrid.getLatitude());
        newGrid.setBoundaryData(sourceGrid.getBoundaryData());
        newGrid.setAddress(sourceGrid.getAddress());
        newGrid.setContactPerson(sourceGrid.getContactPerson());
        newGrid.setContactPhone(sourceGrid.getContactPhone());
        newGrid.setDescription(sourceGrid.getDescription());

        return createGrid(newGrid, operatorId);
    }
}
