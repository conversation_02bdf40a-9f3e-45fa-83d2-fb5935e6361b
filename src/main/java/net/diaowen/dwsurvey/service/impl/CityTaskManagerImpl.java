package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseServiceImpl;
import net.diaowen.common.utils.RandomUtils;
import net.diaowen.dwsurvey.dao.CityTaskDao;
import net.diaowen.dwsurvey.entity.CityTask;
import net.diaowen.dwsurvey.service.CityTaskManager;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 城市任务管理服务实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class CityTaskManagerImpl extends BaseServiceImpl<CityTask, String> implements CityTaskManager {

    @Autowired
    private CityTaskDao cityTaskDao;

    @Autowired
    private UserGridRoleManager userGridRoleManager;

    @Override
    public void setBaseDao() {
        this.baseDao = cityTaskDao;
    }

    @Override
    public Page<CityTask> findTasksByCondition(Page<CityTask> page, Map<String, Object> params) {
        return cityTaskDao.findTasksByCondition(page, params);
    }

    @Override
    @Transactional
    public CityTask createTask(CityTask task, String creatorId) {
        if (task == null) {
            throw new IllegalArgumentException("任务对象不能为空");
        }

        // 设置基本信息
        task.setCreatorId(creatorId);
        task.setCreateDate(new Date());
        
        // 生成任务编码
        if (StringUtils.isBlank(task.getTaskCode())) {
            task.setTaskCode(generateTaskCode());
        }

        // 设置默认状态
        if (StringUtils.isBlank(task.getStatus())) {
            task.setStatus("PENDING");
        }

        // 设置默认优先级
        if (task.getPriority() == null) {
            task.setPriority(3);
        }

        // 设置默认进度
        if (task.getProgress() == null) {
            task.setProgress(0);
        }

        save(task);
        return task;
    }

    @Override
    @Transactional
    public CityTask updateTask(CityTask task, String updaterId) {
        if (task == null || StringUtils.isBlank(task.getId())) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        CityTask existingTask = get(task.getId());
        if (existingTask == null) {
            throw new IllegalArgumentException("任务不存在");
        }

        // 更新基本信息
        task.setUpdaterId(updaterId);
        task.setUpdateDate(new Date());

        save(task);
        return task;
    }

    @Override
    @Transactional
    public boolean assignTask(String taskId, String assigneeId, String updaterId) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(assigneeId)) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        task.setAssigneeId(assigneeId);
        task.setStatus("ASSIGNED");
        task.setUpdaterId(updaterId);
        task.setUpdateDate(new Date());

        save(task);
        return true;
    }

    @Override
    @Transactional
    public int batchAssignTasks(List<String> taskIds, String assigneeId, String updaterId) {
        return cityTaskDao.batchAssignTasks(taskIds, assigneeId, updaterId);
    }

    @Override
    @Transactional
    public boolean startTask(String taskId, String executorId) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(executorId)) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        // 检查任务状态
        if (!"ASSIGNED".equals(task.getStatus()) && !"PENDING".equals(task.getStatus())) {
            return false;
        }

        task.setStatus("IN_PROGRESS");
        task.setStartDate(new Date());
        task.setUpdaterId(executorId);
        task.setUpdateDate(new Date());

        save(task);
        return true;
    }

    @Override
    @Transactional
    public boolean completeTask(String taskId, String executorId, String resultData) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(executorId)) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        task.setStatus("COMPLETED");
        task.setProgress(100);
        task.setCompletedDate(new Date());
        task.setResultData(resultData);
        task.setUpdaterId(executorId);
        task.setUpdateDate(new Date());

        // 计算实际耗时
        if (task.getStartDate() != null) {
            long duration = (new Date().getTime() - task.getStartDate().getTime()) / (1000 * 60); // 分钟
            task.setActualDuration((int) duration);
        }

        save(task);
        return true;
    }

    @Override
    @Transactional
    public boolean cancelTask(String taskId, String operatorId, String reason) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(operatorId)) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        task.setStatus("CANCELLED");
        task.setRemarks(reason);
        task.setUpdaterId(operatorId);
        task.setUpdateDate(new Date());

        save(task);
        return true;
    }

    @Override
    public List<CityTask> findTasksByGridId(String gridId) {
        return cityTaskDao.findByGridId(gridId);
    }

    @Override
    public List<CityTask> findTasksByAssigneeId(String assigneeId) {
        return cityTaskDao.findByAssigneeId(assigneeId);
    }

    @Override
    public List<CityTask> findPendingTasksByUser(String userId) {
        Map<String, Object> params = new HashMap<>();
        params.put("assigneeId", userId);
        params.put("status", "PENDING");
        
        Page<CityTask> page = new Page<>();
        page.setPageSize(100); // 获取前100个待办任务
        
        Page<CityTask> result = findTasksByCondition(page, params);
        return result.getResult();
    }

    @Override
    public List<CityTask> findUrgentTasks() {
        return cityTaskDao.findUrgentTasks();
    }

    @Override
    public List<CityTask> findOverdueTasks() {
        return cityTaskDao.findOverdueTasks();
    }

    @Override
    public CityTask findByTaskCode(String taskCode) {
        return cityTaskDao.findByTaskCode(taskCode);
    }

    @Override
    public String generateTaskCode() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String randomStr = RandomUtils.randomNum(3);
        return "TASK_" + dateStr + "_" + randomStr;
    }

    @Override
    public Map<String, Long> countTasksByAssignee(String assigneeId) {
        return cityTaskDao.countTasksByAssignee(assigneeId);
    }

    @Override
    public Map<String, Long> countTasksByGrid(String gridId) {
        return cityTaskDao.countTasksByGrid(gridId);
    }

    @Override
    public boolean checkTaskPermission(String taskId, String userId, String operation) {
        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        // 检查是否是任务创建者或负责人
        if (userId.equals(task.getCreatorId()) || userId.equals(task.getAssigneeId())) {
            return true;
        }

        // 检查网格权限
        return userGridRoleManager.hasPermission(userId, task.getGridId(), operation);
    }

    @Override
    @Transactional
    public boolean updateTaskProgress(String taskId, Integer progress, String updaterId) {
        if (StringUtils.isBlank(taskId) || progress == null || progress < 0 || progress > 100) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        task.setProgress(progress);
        task.setUpdaterId(updaterId);
        task.setUpdateDate(new Date());

        save(task);
        return true;
    }

    @Override
    @Transactional
    public boolean addTaskAttachment(String taskId, String attachmentInfo, String updaterId) {
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(attachmentInfo)) {
            return false;
        }

        CityTask task = get(taskId);
        if (task == null) {
            return false;
        }

        // 合并附件信息
        String existingAttachments = task.getAttachments();
        if (StringUtils.isBlank(existingAttachments)) {
            task.setAttachments(attachmentInfo);
        } else {
            // 简化处理，实际应该解析JSON数组
            task.setAttachments(existingAttachments + "," + attachmentInfo);
        }

        task.setUpdaterId(updaterId);
        task.setUpdateDate(new Date());

        save(task);
        return true;
    }

    @Override
    @Transactional
    public boolean updateTaskStatus(String taskId, String status, String updaterId) {
        return cityTaskDao.updateTaskStatus(taskId, status, updaterId) > 0;
    }
}
