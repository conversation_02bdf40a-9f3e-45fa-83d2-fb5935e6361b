package net.diaowen.dwsurvey.service.impl;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseServiceImpl;
import net.diaowen.dwsurvey.dao.UserGridRoleDao;
import net.diaowen.dwsurvey.entity.UserGridRole;
import net.diaowen.dwsurvey.service.UserGridRoleManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户网格角色管理服务实现类
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class UserGridRoleManagerImpl extends BaseServiceImpl<UserGridRole, String> implements UserGridRoleManager {

    @Autowired
    private UserGridRoleDao userGridRoleDao;

    @Override
    public void setBaseDao() {
        this.baseDao = userGridRoleDao;
    }

    @Override
    public Page<UserGridRole> findRolesByCondition(Page<UserGridRole> page, Map<String, Object> params) {
        return userGridRoleDao.findRolesByCondition(page, params);
    }

    @Override
    @Transactional
    public UserGridRole assignUserToGrid(String userId, String gridId, String roleCode, List<String> permissions, String creatorId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(gridId) || StringUtils.isBlank(roleCode)) {
            throw new IllegalArgumentException("用户ID、网格ID和角色代码不能为空");
        }

        // 检查是否已存在相同角色
        UserGridRole existingRole = userGridRoleDao.findByUserIdAndGridIdAndRoleCode(userId, gridId, roleCode);
        if (existingRole != null) {
            throw new IllegalStateException("用户已拥有该网格的此角色");
        }

        UserGridRole role = new UserGridRole();
        role.setUserId(userId);
        role.setGridId(gridId);
        role.setRoleCode(roleCode);
        role.setRoleName(getRoleNameByCode(roleCode));
        role.setPermissions(convertPermissionsToJson(permissions));
        role.setCreatorId(creatorId);
        role.setAssignDate(new Date());
        role.setCreateDate(new Date());
        role.setStatus(1);

        save(role);
        return role;
    }

    @Override
    @Transactional
    public int batchAssignUsersToGrid(List<String> userIds, String gridId, String roleCode, List<String> permissions, String creatorId) {
        return userGridRoleDao.batchAssignRoles(userIds, gridId, roleCode, creatorId);
    }

    @Override
    @Transactional
    public boolean removeUserFromGrid(String userId, String gridId, String roleCode, String operatorId) {
        return userGridRoleDao.deleteUserRole(userId, gridId, roleCode) > 0;
    }

    @Override
    @Transactional
    public int removeUserFromGridAllRoles(String userId, String gridId, String operatorId) {
        return userGridRoleDao.deleteUserGridRoles(userId, gridId);
    }

    @Override
    @Transactional
    public boolean updateUserPermissions(String userId, String gridId, String roleCode, List<String> permissions, String updaterId) {
        UserGridRole role = userGridRoleDao.findByUserIdAndGridIdAndRoleCode(userId, gridId, roleCode);
        if (role == null) {
            return false;
        }

        role.setPermissions(convertPermissionsToJson(permissions));
        role.setUpdaterId(updaterId);
        role.setUpdateDate(new Date());

        save(role);
        return true;
    }

    @Override
    public List<UserGridRole> findRolesByUserId(String userId) {
        return userGridRoleDao.findByUserId(userId);
    }

    @Override
    public List<UserGridRole> findRolesByGridId(String gridId) {
        return userGridRoleDao.findByGridId(gridId);
    }

    @Override
    public List<UserGridRole> findRolesByRoleCode(String roleCode) {
        return userGridRoleDao.findByRoleCode(roleCode);
    }

    @Override
    public List<UserGridRole> findRolesByUserAndGrid(String userId, String gridId) {
        return userGridRoleDao.findByUserIdAndGridId(userId, gridId);
    }

    @Override
    public List<String> findUserPermissions(String userId, String gridId) {
        return userGridRoleDao.findUserPermissions(userId, gridId);
    }

    @Override
    public List<UserGridRole> findGridAdmins(String gridId) {
        return userGridRoleDao.findGridAdmins(gridId);
    }

    @Override
    public List<UserGridRole> findGridManagers(String gridId) {
        return userGridRoleDao.findGridManagers(gridId);
    }

    @Override
    public List<UserGridRole> findDataCollectors(String gridId) {
        return userGridRoleDao.findDataCollectors(gridId);
    }

    @Override
    public boolean hasPermission(String userId, String gridId, String permission) {
        return userGridRoleDao.hasPermission(userId, gridId, permission);
    }

    @Override
    public boolean hasRole(String userId, String gridId, String roleCode) {
        return userGridRoleDao.hasRole(userId, gridId, roleCode);
    }

    @Override
    public boolean isGridAdmin(String userId, String gridId) {
        return hasRole(userId, gridId, "GRID_ADMIN");
    }

    @Override
    public boolean isGridManager(String userId, String gridId) {
        return hasRole(userId, gridId, "GRID_MANAGER");
    }

    @Override
    public boolean isDataCollector(String userId, String gridId) {
        return hasRole(userId, gridId, "DATA_COLLECTOR");
    }

    @Override
    @Transactional
    public boolean enableRole(String roleId, String operatorId) {
        return userGridRoleDao.updateRoleStatus(roleId, 1, operatorId) > 0;
    }

    @Override
    @Transactional
    public boolean disableRole(String roleId, String operatorId) {
        return userGridRoleDao.updateRoleStatus(roleId, 0, operatorId) > 0;
    }

    @Override
    @Transactional
    public boolean transferGridManager(String gridId, String fromUserId, String toUserId, String operatorId) {
        // 移除原负责人角色
        removeUserFromGrid(fromUserId, gridId, "GRID_MANAGER", operatorId);
        
        // 分配新负责人角色
        List<String> permissions = Arrays.asList("VIEW", "CREATE", "UPDATE", "DELETE", "ASSIGN");
        assignUserToGrid(toUserId, gridId, "GRID_MANAGER", permissions, operatorId);
        
        return true;
    }

    @Override
    public List<String> findManageableGrids(String userId) {
        List<UserGridRole> roles = findRolesByUserId(userId);
        return roles.stream()
                .filter(role -> "GRID_ADMIN".equals(role.getRoleCode()) || "GRID_MANAGER".equals(role.getRoleCode()))
                .map(UserGridRole::getGridId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findAccessibleGrids(String userId) {
        List<UserGridRole> roles = findRolesByUserId(userId);
        return roles.stream()
                .map(UserGridRole::getGridId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据角色代码获取角色名称
     */
    private String getRoleNameByCode(String roleCode) {
        switch (roleCode) {
            case "GRID_ADMIN":
                return "网格管理员";
            case "GRID_MANAGER":
                return "网格负责人";
            case "DATA_COLLECTOR":
                return "数据采集员";
            default:
                return roleCode;
        }
    }

    /**
     * 将权限列表转换为JSON字符串
     */
    private String convertPermissionsToJson(List<String> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return "[]";
        }
        return "[\"" + String.join("\",\"", permissions) + "\"]";
    }

    /**
     * 将JSON字符串转换为权限列表
     */
    private List<String> convertJsonToPermissions(String permissionsJson) {
        if (StringUtils.isBlank(permissionsJson) || "[]".equals(permissionsJson)) {
            return new ArrayList<>();
        }
        
        // 简化处理，实际应该使用JSON解析
        String[] perms = permissionsJson.replace("[", "").replace("]", "").replace("\"", "").split(",");
        List<String> result = new ArrayList<>();
        for (String perm : perms) {
            if (StringUtils.isNotBlank(perm.trim())) {
                result.add(perm.trim());
            }
        }
        return result;
    }
}
