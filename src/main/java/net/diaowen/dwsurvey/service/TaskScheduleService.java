package net.diaowen.dwsurvey.service;

import net.diaowen.dwsurvey.entity.CityTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 任务调度服务
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Service
public class TaskScheduleService {

    private static final Logger logger = LoggerFactory.getLogger(TaskScheduleService.class);

    @Autowired
    private CityTaskManager cityTaskManager;

    @Autowired
    private TaskPushService taskPushService;

    /**
     * 每小时检查一次任务截止提醒
     * cron表达式: 0 0 * * * ? (每小时的0分0秒执行)
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkTaskDeadlineReminders() {
        logger.info("开始执行任务截止提醒检查");
        try {
            taskPushService.checkAndPushDeadlineReminders();
            logger.info("任务截止提醒检查完成");
        } catch (Exception e) {
            logger.error("任务截止提醒检查失败", e);
        }
    }

    /**
     * 每天凌晨2点检查逾期任务
     * cron表达式: 0 0 2 * * ? (每天凌晨2点执行)
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void checkOverdueTasks() {
        logger.info("开始执行逾期任务检查");
        try {
            Date now = new Date();
            
            // 查询所有进行中和已分配的任务
            List<CityTask> inProgressTasks = cityTaskManager.findByStatus("IN_PROGRESS");
            List<CityTask> assignedTasks = cityTaskManager.findByStatus("ASSIGNED");

            int overdueCount = 0;

            // 检查进行中的任务
            for (CityTask task : inProgressTasks) {
                if (task.getDeadline() != null && task.getDeadline().before(now)) {
                    cityTaskManager.updateTaskStatus(task.getId(), "OVERDUE", "system");
                    taskPushService.pushTaskStatusUpdateNotification(task);
                    overdueCount++;
                }
            }

            // 检查已分配的任务
            for (CityTask task : assignedTasks) {
                if (task.getDeadline() != null && task.getDeadline().before(now)) {
                    cityTaskManager.updateTaskStatus(task.getId(), "OVERDUE", "system");
                    taskPushService.pushTaskStatusUpdateNotification(task);
                    overdueCount++;
                }
            }

            logger.info("逾期任务检查完成，共处理 {} 个逾期任务", overdueCount);
        } catch (Exception e) {
            logger.error("逾期任务检查失败", e);
        }
    }

    /**
     * 每30分钟清理WebSocket连接
     * cron表达式: (每30分钟执行一次)
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void cleanupWebSocketConnections() {
        logger.info("开始清理WebSocket连接");
        try {
            // 这里可以添加清理逻辑，比如检查无效连接等
            // webSocketHandler.cleanupInvalidConnections();
            logger.info("WebSocket连接清理完成");
        } catch (Exception e) {
            logger.error("WebSocket连接清理失败", e);
        }
    }

    /**
     * 每天凌晨3点生成任务统计数据
     * cron表达式: 0 0 3 * * ? (每天凌晨3点执行)
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void generateTaskStatistics() {
        logger.info("开始生成任务统计数据");
        try {
            // 这里可以添加统计数据生成逻辑
            // taskStatisticsService.generateDailyStatistics();
            logger.info("任务统计数据生成完成");
        } catch (Exception e) {
            logger.error("任务统计数据生成失败", e);
        }
    }

    /**
     * 每周日凌晨4点清理过期日志
     * cron表达式: 0 0 4 ? * SUN (每周日凌晨4点执行)
     */
    @Scheduled(cron = "0 0 4 ? * SUN")
    public void cleanupExpiredLogs() {
        logger.info("开始清理过期日志");
        try {
            // 这里可以添加日志清理逻辑
            // logCleanupService.cleanupExpiredLogs();
            logger.info("过期日志清理完成");
        } catch (Exception e) {
            logger.error("过期日志清理失败", e);
        }
    }

    /**
     * 每15分钟检查紧急任务
     * cron表达式: (每15分钟执行一次)
     */
    @Scheduled(cron = "0 */15 * * * ?")
    public void checkUrgentTasks() {
        logger.info("开始检查紧急任务");
        try {
            List<CityTask> urgentTasks = cityTaskManager.findUrgentTasks();
            
            for (CityTask task : urgentTasks) {
                // 如果是新创建的紧急任务（创建时间在15分钟内），重新推送通知
                long timeDiff = System.currentTimeMillis() - task.getCreateDate().getTime();
                if (timeDiff <= 15 * 60 * 1000) { // 15分钟内
                    taskPushService.pushUrgentTaskToAllUsers(task);
                }
            }

            logger.info("紧急任务检查完成，共检查 {} 个紧急任务", urgentTasks.size());
        } catch (Exception e) {
            logger.error("紧急任务检查失败", e);
        }
    }

    /**
     * 每5分钟检查任务自动分配
     * cron表达式:(每5分钟执行一次)
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void autoAssignTasks() {
        logger.info("开始执行任务自动分配");
        try {
            // 查询待处理的任务
            List<CityTask> pendingTasks = cityTaskManager.findByStatus("PENDING");
            
            int assignedCount = 0;
            for (CityTask task : pendingTasks) {
                // 这里可以添加自动分配逻辑
                // 比如根据网格、任务类型、负载均衡等规则自动分配任务
                // if (autoAssignTask(task)) {
                //     assignedCount++;
                // }
            }

            logger.info("任务自动分配完成，共分配 {} 个任务", assignedCount);
        } catch (Exception e) {
            logger.error("任务自动分配失败", e);
        }
    }

    /**
     * 每天凌晨1点备份重要数据
     * cron表达式: 0 0 1 * * ? (每天凌晨1点执行)
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void backupImportantData() {
        logger.info("开始备份重要数据");
        try {
            // 这里可以添加数据备份逻辑
            // dataBackupService.backupTaskData();
            logger.info("重要数据备份完成");
        } catch (Exception e) {
            logger.error("重要数据备份失败", e);
        }
    }

    /**
     * 每小时检查系统健康状态
     * cron表达式: 0 30 * * * ? (每小时的30分执行)
     */
    @Scheduled(cron = "0 30 * * * ?")
    public void checkSystemHealth() {
        logger.info("开始检查系统健康状态");
        try {
            // 检查数据库连接
            // 检查WebSocket连接数
            // 检查内存使用情况
            // 检查任务处理性能
            
            logger.info("系统健康状态检查完成");
        } catch (Exception e) {
            logger.error("系统健康状态检查失败", e);
        }
    }
}
