package net.diaowen.dwsurvey.service;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseService;
import net.diaowen.dwsurvey.entity.UserGridRole;

import java.util.List;
import java.util.Map;

/**
 * 用户网格角色管理服务接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface UserGridRoleManager extends BaseService<UserGridRole, String> {

    /**
     * 根据条件分页查询用户角色列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<UserGridRole> findRolesByCondition(Page<UserGridRole> page, Map<String, Object> params);

    /**
     * 分配用户到网格角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @param permissions 权限列表
     * @param creatorId 创建人ID
     * @return 分配的角色
     */
    UserGridRole assignUserToGrid(String userId, String gridId, String roleCode, List<String> permissions, String creatorId);

    /**
     * 批量分配用户到网格角色
     * @param userIds 用户ID列表
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @param permissions 权限列表
     * @param creatorId 创建人ID
     * @return 分配成功的数量
     */
    int batchAssignUsersToGrid(List<String> userIds, String gridId, String roleCode, List<String> permissions, String creatorId);

    /**
     * 移除用户的网格角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean removeUserFromGrid(String userId, String gridId, String roleCode, String operatorId);

    /**
     * 移除用户在指定网格的所有角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param operatorId 操作人ID
     * @return 移除的角色数量
     */
    int removeUserFromGridAllRoles(String userId, String gridId, String operatorId);

    /**
     * 更新用户角色权限
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @param permissions 新权限列表
     * @param updaterId 更新人ID
     * @return 是否成功
     */
    boolean updateUserPermissions(String userId, String gridId, String roleCode, List<String> permissions, String updaterId);

    /**
     * 根据用户ID查询角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<UserGridRole> findRolesByUserId(String userId);

    /**
     * 根据网格ID查询角色列表
     * @param gridId 网格ID
     * @return 角色列表
     */
    List<UserGridRole> findRolesByGridId(String gridId);

    /**
     * 根据角色代码查询角色列表
     * @param roleCode 角色代码
     * @return 角色列表
     */
    List<UserGridRole> findRolesByRoleCode(String roleCode);

    /**
     * 根据用户ID和网格ID查询角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 角色列表
     */
    List<UserGridRole> findRolesByUserAndGrid(String userId, String gridId);

    /**
     * 查询用户在指定网格的权限
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 权限列表
     */
    List<String> findUserPermissions(String userId, String gridId);

    /**
     * 查询网格管理员列表
     * @param gridId 网格ID
     * @return 管理员列表
     */
    List<UserGridRole> findGridAdmins(String gridId);

    /**
     * 查询网格负责人列表
     * @param gridId 网格ID
     * @return 负责人列表
     */
    List<UserGridRole> findGridManagers(String gridId);

    /**
     * 查询数据采集员列表
     * @param gridId 网格ID
     * @return 采集员列表
     */
    List<UserGridRole> findDataCollectors(String gridId);

    /**
     * 检查用户是否有指定网格的权限
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param permission 权限代码
     * @return 是否有权限
     */
    boolean hasPermission(String userId, String gridId, String permission);

    /**
     * 检查用户是否有指定角色
     * @param userId 用户ID
     * @param gridId 网格ID
     * @param roleCode 角色代码
     * @return 是否有角色
     */
    boolean hasRole(String userId, String gridId, String roleCode);

    /**
     * 检查用户是否是网格管理员
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 是否是管理员
     */
    boolean isGridAdmin(String userId, String gridId);

    /**
     * 检查用户是否是网格负责人
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 是否是负责人
     */
    boolean isGridManager(String userId, String gridId);

    /**
     * 检查用户是否是数据采集员
     * @param userId 用户ID
     * @param gridId 网格ID
     * @return 是否是采集员
     */
    boolean isDataCollector(String userId, String gridId);

    /**
     * 启用用户角色
     * @param roleId 角色ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean enableRole(String roleId, String operatorId);

    /**
     * 禁用用户角色
     * @param roleId 角色ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean disableRole(String roleId, String operatorId);

    /**
     * 转移网格负责人
     * @param gridId 网格ID
     * @param fromUserId 原负责人ID
     * @param toUserId 新负责人ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean transferGridManager(String gridId, String fromUserId, String toUserId, String operatorId);

    /**
     * 获取用户可管理的网格列表
     * @param userId 用户ID
     * @return 网格ID列表
     */
    List<String> findManageableGrids(String userId);

    /**
     * 获取用户可访问的网格列表
     * @param userId 用户ID
     * @return 网格ID列表
     */
    List<String> findAccessibleGrids(String userId);
}
