package net.diaowen.dwsurvey.service;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseService;
import net.diaowen.dwsurvey.entity.TaskGrid;

import java.util.List;
import java.util.Map;

/**
 * 网格管理服务接口
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
public interface TaskGridManager extends BaseService<TaskGrid, String> {

    /**
     * 根据条件分页查询网格列表
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    Page<TaskGrid> findGridsByCondition(Page<TaskGrid> page, Map<String, Object> params);

    /**
     * 创建网格
     * @param grid 网格对象
     * @param creatorId 创建人ID
     * @return 创建的网格
     */
    TaskGrid createGrid(TaskGrid grid, String creatorId);

    /**
     * 更新网格
     * @param grid 网格对象
     * @param updaterId 更新人ID
     * @return 更新的网格
     */
    TaskGrid updateGrid(TaskGrid grid, String updaterId);

    /**
     * 删除网格
     * @param gridId 网格ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean deleteGrid(String gridId, String operatorId);

    /**
     * 根据父网格ID查询子网格列表
     * @param parentId 父网格ID
     * @return 子网格列表
     */
    List<TaskGrid> findChildGrids(String parentId);

    /**
     * 根据网格类型查询网格列表
     * @param gridType 网格类型
     * @return 网格列表
     */
    List<TaskGrid> findGridsByType(String gridType);

    /**
     * 根据网格层级查询网格列表
     * @param gridLevel 网格层级
     * @return 网格列表
     */
    List<TaskGrid> findGridsByLevel(Integer gridLevel);

    /**
     * 根据网格编码查询网格
     * @param gridCode 网格编码
     * @return 网格对象
     */
    TaskGrid findByGridCode(String gridCode);

    /**
     * 查询根网格列表（顶级网格）
     * @return 根网格列表
     */
    List<TaskGrid> findRootGrids();

    /**
     * 构建网格树形结构
     * @return 网格树形结构
     */
    List<Map<String, Object>> buildGridTree();

    /**
     * 构建指定网格的子树
     * @param parentId 父网格ID
     * @return 子树结构
     */
    List<Map<String, Object>> buildGridSubTree(String parentId);

    /**
     * 根据区域代码查询网格
     * @param areaCode 区域代码
     * @return 网格对象
     */
    TaskGrid findByAreaCode(String areaCode);

    /**
     * 查询指定网格的所有子网格（递归）
     * @param gridId 网格ID
     * @return 子网格列表
     */
    List<TaskGrid> findAllChildGrids(String gridId);

    /**
     * 查询指定网格的路径（从根到当前网格）
     * @param gridId 网格ID
     * @return 网格路径
     */
    List<TaskGrid> findGridPath(String gridId);

    /**
     * 根据坐标查询网格
     * @param longitude 经度
     * @param latitude 纬度
     * @return 网格列表
     */
    List<TaskGrid> findGridsByLocation(Double longitude, Double latitude);

    /**
     * 生成网格编码
     * @param parentId 父网格ID
     * @param gridType 网格类型
     * @return 网格编码
     */
    String generateGridCode(String parentId, String gridType);

    /**
     * 检查网格编码是否存在
     * @param gridCode 网格编码
     * @return 是否存在
     */
    boolean isGridCodeExists(String gridCode);

    /**
     * 统计网格下的任务数量
     * @param gridId 网格ID
     * @return 任务数量
     */
    Long countTasksByGrid(String gridId);

    /**
     * 检查网格权限
     * @param gridId 网格ID
     * @param userId 用户ID
     * @param operation 操作类型
     * @return 是否有权限
     */
    boolean checkGridPermission(String gridId, String userId, String operation);

    /**
     * 启用网格
     * @param gridId 网格ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean enableGrid(String gridId, String operatorId);

    /**
     * 禁用网格
     * @param gridId 网格ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean disableGrid(String gridId, String operatorId);

    /**
     * 移动网格到新的父网格
     * @param gridId 网格ID
     * @param newParentId 新父网格ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean moveGrid(String gridId, String newParentId, String operatorId);

    /**
     * 复制网格
     * @param sourceGridId 源网格ID
     * @param targetParentId 目标父网格ID
     * @param operatorId 操作人ID
     * @return 复制的网格
     */
    TaskGrid copyGrid(String sourceGridId, String targetParentId, String operatorId);
}
