package net.diaowen.dwsurvey.entity;

import net.diaowen.common.base.entity.IdEntity;

import javax.persistence.*;
import java.util.Date;

/**
 * 用户网格角色实体
 * <AUTHOR> Team
 * 
 * https://github.com/wkeyuan/DWSurvey
 * http://dwsurvey.net
 */
@Entity
@Table(name = "t_user_grid_role")
public class UserGridRole extends IdEntity {

    @Column(name = "user_id", nullable = false, length = 32)
    private String userId;

    @Column(name = "grid_id", nullable = false, length = 32)
    private String gridId;

    @Column(name = "role_code", nullable = false, length = 50)
    private String roleCode; // GRID_ADMIN-网格管理员,GRID_MANAGER-网格负责人,DATA_COLLECTOR-数据采集员

    @Column(name = "role_name", nullable = false, length = 100)
    private String roleName;

    @Column(name = "permissions", columnDefinition = "TEXT")
    private String permissions; // JSON格式

    @Column(name = "status", nullable = false)
    private Integer status = 1; // 0-禁用,1-启用

    @Column(name = "assign_date", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date assignDate = new Date();

    @Column(name = "expire_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expireDate;

    @Column(name = "create_date", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate = new Date();

    @Column(name = "update_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name = "creator_id", length = 32)
    private String creatorId;

    @Column(name = "updater_id", length = 32)
    private String updaterId;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getGridId() {
        return gridId;
    }

    public void setGridId(String gridId) {
        this.gridId = gridId;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getAssignDate() {
        return assignDate;
    }

    public void setAssignDate(Date assignDate) {
        this.assignDate = assignDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }
}
