/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package net.diaowen.common.plugs.security;

import net.diaowen.common.base.entity.User;
import net.diaowen.common.base.service.AccountManager;
import net.diaowen.dwsurvey.common.RoleCode;
import org.apache.shiro.authc.*;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ShiroDbRealm extends AuthorizingRealm {

	@Autowired
	protected AccountManager accountManager;

	public ShiroDbRealm() {
		setCredentialsMatcher(new HashedCredentialsMatcher("SHA-1"));
	}

	/**
	 * 认证回调函数,登录时调用.
	 */
	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken) throws AuthenticationException {
		UsernamePasswordToken token = (UsernamePasswordToken) authcToken;
//		User user = accountManager.findUserByLoginName(token.getUsername());

		//根据loginToken 看能不查到当前token token有效期就1分钟

		String tokenPassword=new String(token.getPassword());

		User user = accountManager.findUserByLoginNameOrEmail(token.getUsername());

		//user.getStandardLock()==1
		if (user != null && user.getStatus().intValue()!=0 && (user.getVisibility()==null || user.getVisibility()==1 )) {
			 return new SimpleAuthenticationInfo(user.getLoginName(), user.getShaPassword() , getName());
		} else {
			return null;
		}
	}

	/**
	 * 授权查询回调函数, 进行鉴权但缓存中无用户的授权信息时调用.
	 */
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
		String username = (String) principals.fromRealm(getName()).iterator().next();
//		User user = accountManager.findUserByLoginName(username);
		User user = accountManager.findUserByLoginNameOrEmail(username);
		if(user!=null){
			SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
			if ("1".equals(user.getId())) {
				info.addRole(RoleCode.SUPER_ADMIN);
			}
			return info;
		}
		return null;
	}

	/**
	 * 更新用户授权信息缓存.
	 */
	public void clearCachedAuthorizationInfo(String principal) {
		SimplePrincipalCollection principals = new SimplePrincipalCollection(principal, getName());
		clearCachedAuthorizationInfo(principals);
	}

}
