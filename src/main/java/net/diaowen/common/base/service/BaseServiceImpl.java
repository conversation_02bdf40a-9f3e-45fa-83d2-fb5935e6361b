package net.diaowen.common.base.service;

import net.diaowen.common.base.dao.BaseDao;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

@Transactional
public abstract class BaseServiceImpl<T, ID extends Serializable> implements BaseService<T, ID> {

    protected BaseDao<T, ID> baseDao;

    public abstract void setBaseDao(BaseDao<T, ID> baseDao);

    @Override
    public T save(T entity) {
        baseDao.save(entity);
        return entity;
    }

    @Override
    @Transactional(readOnly = true)
    public T get(ID id) {
        return baseDao.get(id);
    }

    @Override
    public void delete(ID id) {
        baseDao.delete(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<T> findAll() {
        return baseDao.findAll();
    }
}