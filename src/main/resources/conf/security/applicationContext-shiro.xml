<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd"
	default-lazy-init="true">

	<description><PERSON>ro安全配置</description>

	<bean id="shiroDbRealm" class="net.diaowen.common.plugs.security.ShiroDbRealm" />

	<!-- <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realm" ref="shiroDbRealm" />
		<property name="rememberMeManager" ref="rememberMeManager"/>
	</bean>
	 -->
  <bean id="securityManager" class="net.diaowen.common.plugs.security.MyDefaultWebSecurityManager">
    <property name="realm" ref="shiroDbRealm" />
    <property name="rememberMeManager" ref="rememberMeManager"/>
    <!--<property name="sessionManager" ref="sessionManager" />-->
  </bean>

  <!--<bean id="sessionManager" class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">
    <property name="sessionIdUrlRewritingEnabled" value="false" />
  </bean>-->

	<bean id="formAuthFilter" class="net.diaowen.common.plugs.security.filter.FormAuthenticationWithLockFilter">
		<property name="maxLoginAttempts" value="100"/>
		<property name="successAdminUrl" value="/design/my-survey/list.do"/>
		<property name="successAdminRole" value="admin"/>
		<property name="rememberMeParam" value="rememberMe"/>
	</bean>

	<bean id="userAuthFilter" class="net.diaowen.common.plugs.security.filter.MyUserFilter">
	</bean>

	<bean id="roleOrFilter" class="net.diaowen.common.plugs.security.RolesOrAuthorizationFilter"></bean>

	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="/login.jsp" />
		<property name="successUrl" value="/design/my-survey/list.do" />
		<property name="unauthorizedUrl" value="/login.jsp?una=0" />
		<property name="filters">
			<util:map>
				<entry key="authc" value-ref="formAuthFilter"></entry>
				<entry key="user" value-ref="userAuthFilter"></entry>
				<entry key="roleOrFilter" value-ref="roleOrFilter"/>
			</util:map>
		</property>
		<property name="filterChainDefinitions">
			<value>
				/api/dwsurvey/anon/security/** = anon
				/api/dwsurvey/anon/jcap/** = anon
				/api/dwsurvey/anon/response/** = anon
				/login.jsp = authc
				/ic/** = user
				/design/** = user
				/da/** = user
				/api/dwsurvey/app/survey/** = roleOrFilter[aewoo]
				/api/dwsurvey/app/** = user
				/api/dwsurvey/admin/** = user
				/sy/** = roles[admin]
			</value>
		</property>
	</bean>

	<bean id="cacheManager" class="org.apache.shiro.cache.MemoryConstrainedCacheManager" />

	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor" />

	<!-- 使用记住我功能 -->
	<!-- 会话Cookie模板 -->
	<bean id="sessionIdCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
	    <constructor-arg value="sid"/>
	    <property name="httpOnly" value="true"/>
	    <property name="maxAge" value="-1"/>
	</bean>
	<bean id="rememberMeCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
	    <constructor-arg value="rememberMe"/>
	    <property name="httpOnly" value="true"/>
	    <property name="maxAge" value="2592000"/><!-- 30天  (maxAge=-1表示浏览器关闭时失效) -->
	</bean>
	<!-- rememberMe管理器 -->
	<bean id="rememberMeManager" class="org.apache.shiro.web.mgt.CookieRememberMeManager">
	    <!--<property name="cipherKey" value="#{T(org.apache.shiro.codec.Base64).decode('4AvVhmFLUs0KTA3Kprsdag==')}"/>-->
	     <property name="cookie" ref="rememberMeCookie"/>
	</bean>

</beans>
