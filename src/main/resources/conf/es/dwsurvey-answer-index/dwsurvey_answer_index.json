{"settings": {"index": {"number_of_shards": 3, "number_of_replicas": 1}}, "mappings": {"properties": {"anPwd": {"type": "keyword"}, "anQuestions": {"properties": {"anCascades": {"properties": {"level": {"type": "integer"}, "value": {"type": "keyword"}}}, "anCheckboxs": {"properties": {"optionDwId": {"type": "keyword"}, "otherText": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}}}, "anFbk": {"properties": {"answer": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}}}, "anMFbks": {"properties": {"answer": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "optionDwId": {"type": "keyword"}}}, "anMatrixCheckboxes": {"properties": {"rowAnCheckboxs": {"properties": {"optionDwId": {"type": "keyword"}, "otherText": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}}}, "rowAnScore": {"type": "float"}, "rowDwId": {"type": "keyword"}}}, "anMatrixFbks": {"properties": {"rowAnFbks": {"properties": {"answer": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "optionDwId": {"type": "keyword"}}}, "rowDwId": {"type": "keyword"}}}, "anMatrixRadios": {"properties": {"colDwId": {"type": "keyword"}, "rowAnScore": {"type": "float"}, "rowDwId": {"type": "keyword"}}}, "anMatrixScales": {"properties": {"answerScore": {"type": "float"}, "rowAnScore": {"type": "float"}, "rowDwId": {"type": "keyword"}}}, "anOrders": {"properties": {"optionDwId": {"type": "keyword"}, "orderNum": {"type": "integer"}, "otherText": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}}}, "anRadio": {"properties": {"optionDwId": {"type": "keyword"}, "otherText": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}}}, "anScale": {"properties": {"answerScore": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "anScores": {"properties": {"answerScore": {"type": "float"}, "optionDwId": {"type": "keyword"}}}, "anUploadFiles": {"properties": {"fileName": {"type": "keyword"}, "filePath": {"type": "keyword"}}}, "quAnScore": {"type": "float"}, "quDwId": {"type": "keyword"}, "quType": {"type": "keyword"}}}, "answerCommon": {"properties": {"anIp": {"properties": {"addr": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "city": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "cityV6": {"type": "keyword"}, "county": {"type": "keyword"}, "ip": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "province": {"type": "keyword"}, "town": {"type": "keyword"}}}, "anSource": {"properties": {"anClient": {"type": "keyword"}, "bro": {"type": "keyword"}, "dbSource": {"type": "keyword"}, "pcOrM": {"type": "keyword"}, "pcm": {"type": "keyword"}, "sys": {"type": "keyword"}, "userAgentString": {"type": "text", "analyzer": "standard"}}}, "anState": {"properties": {"anQuNum": {"type": "integer"}, "handleState": {"type": "integer"}, "isEff": {"type": "integer"}}}, "anTime": {"properties": {"bgAnDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "endAnDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "totalTime": {"type": "long"}}}, "anUser": {"properties": {"contactsEsId": {"type": "keyword"}, "dwEsContacts": {"properties": {"adminUserId": {"type": "keyword"}, "createDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "customAttrs": {"properties": {"attrName": {"type": "keyword"}, "attrValue": {"type": "keyword"}}}, "dwContactsDepts": {"properties": {"deptCode": {"type": "keyword"}, "deptId": {"type": "keyword"}, "deptName": {"type": "keyword"}}}, "dwContactsGroups": {"properties": {"groupId": {"type": "keyword"}, "groupName": {"type": "keyword"}}}, "dwContactsJobTitles": {"properties": {"roleId": {"type": "keyword"}, "roleName": {"type": "keyword"}}}, "dwContactsTags": {"properties": {"tagId": {"type": "keyword"}, "tagName": {"type": "keyword"}}}, "esId": {"type": "keyword"}, "initPwd": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "loginName": {"type": "keyword"}, "mail": {"type": "keyword"}, "phone": {"type": "keyword"}, "repeatData": {"type": "boolean"}, "teamId": {"type": "keyword"}, "uname": {"type": "keyword"}, "userCheck": {"type": "long"}, "userId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "wxOpenId": {"type": "keyword"}}}, "relateContactEsId": {"type": "keyword"}, "userId": {"type": "keyword"}, "userName": {"type": "keyword"}}}, "answerId": {"type": "keyword"}, "answerDwId": {"type": "keyword"}, "isDelete": {"type": "long"}, "sid": {"type": "keyword"}, "sumScore": {"type": "float"}, "surveyDwId": {"type": "keyword"}, "surveyId": {"type": "keyword"}}}, "editCommon": {"properties": {"anIp": {"properties": {"addr": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "city": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word"}, "cityV6": {"type": "keyword"}, "county": {"type": "keyword"}, "ip": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "province": {"type": "keyword"}, "town": {"type": "keyword"}}}, "anSource": {"properties": {"anClient": {"type": "keyword"}, "bro": {"type": "keyword"}, "dbSource": {"type": "keyword"}, "pcOrM": {"type": "keyword"}, "pcm": {"type": "keyword"}, "sys": {"type": "keyword"}, "userAgentString": {"type": "text", "analyzer": "standard"}}}, "anTime": {"properties": {"bgAnDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "endAnDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "totalTime": {"type": "long"}}}, "anUser": {"properties": {"contactsEsId": {"type": "keyword"}, "dwEsContacts": {"properties": {"adminUserId": {"type": "keyword"}, "createDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"}, "customAttrs": {"properties": {"attrName": {"type": "keyword"}, "attrValue": {"type": "keyword"}}}, "dwContactsDepts": {"properties": {"deptCode": {"type": "keyword"}, "deptId": {"type": "keyword"}, "deptName": {"type": "keyword"}}}, "dwContactsGroups": {"properties": {"groupId": {"type": "keyword"}, "groupName": {"type": "keyword"}}}, "dwContactsJobTitles": {"properties": {"roleId": {"type": "keyword"}, "roleName": {"type": "keyword"}}}, "dwContactsTags": {"properties": {"tagId": {"type": "keyword"}, "tagName": {"type": "keyword"}}}, "esId": {"type": "keyword"}, "initPwd": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "loginName": {"type": "keyword"}, "mail": {"type": "keyword"}, "phone": {"type": "keyword"}, "repeatData": {"type": "boolean"}, "teamId": {"type": "keyword"}, "uname": {"type": "keyword"}, "userCheck": {"type": "long"}, "userId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "wxOpenId": {"type": "keyword"}}}, "relateContactEsId": {"type": "keyword"}, "userId": {"type": "keyword"}, "userName": {"type": "keyword"}}}}}}}}