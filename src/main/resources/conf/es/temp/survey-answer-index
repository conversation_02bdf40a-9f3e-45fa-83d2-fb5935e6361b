PUT dwsurvey_answer_index
{
  "settings": {
    "index": {
      "number_of_shards": 3,
      "number_of_replicas": 1
    }
  },
  "mappings": {
    "properties": {
      "answerCommon": {
        "properties": {
          "anIp": {
            "properties": {
              "addr": {
                "analyzer": "ik_max_word",
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              },
              "province": {
                  "type": "keyword"
                },
                "city": {
                  "type": "keyword"
                },
                "county": {
                  "type": "keyword"
                 },
                "town": {
                  "type": "keyword"
                },
              "ip": {
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              }
            }
          },
          "anState": {
            "properties": {
              "anQuNum": {
                "type": "integer"
              },
              "handleState": {
                "type": "integer"
              },
              "isEff": {
                "type": "integer"
              }
            }
          },
          "anTime": {
            "properties": {
              "bgAnDate": {
                "type":   "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
              },
              "endAnDate": {
                "type":   "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
              },
              "totalTime": {
                "type": "long"
              }
            }
          },
          "anUser": {
            "properties": {
              "userId": {
                "type": "keyword"
              },
              "userName": {
                "type": "keyword"
              }
            }
          },
          "surveyDwId": {
            "type": "keyword"
          },
          "answerDwId": {
            "type": "keyword"
          },
          "surveyId": {
            "type": "keyword"
          },
          "sid": {
            "type": "keyword"
          },
          "sumScore": {
            "type": "float"
          }
        }
      },
      "anQuestions": {
        "properties": {
          "anCheckboxs": {
            "properties": {
              "optionDwId": {
                "type": "keyword"
              },
              "otherText": {
                "analyzer": "ik_max_word",
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              }
            }
          },
          "anFbk": {
            "properties": {
              "answer": {
                "analyzer": "ik_max_word",
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              }
            }
          },
          "anMFbks": {
            "properties": {
              "answer": {
                "analyzer": "ik_max_word",
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              },
              "optionDwId": {
                "type": "keyword"
              }
            }
          },
          "anOrders": {
            "properties": {
              "optionDwId": {
                "type": "keyword"
              },
              "orderNum": {
                "type": "integer"
              }
            }
          },
          "anRadio": {
            "properties": {
              "optionDwId": {
                "type": "keyword"
              },
              "otherText": {
                "analyzer": "ik_max_word",
                "type": "text",
                "fields": {
                  "keyword": {
                    "type": "keyword",
                    "ignore_above": 256
                  }
                }
              }
            }
          },
          "anScores": {
            "properties": {
              "answerScore": {
                "type": "float"
              },
              "optionDwId": {
                "type": "keyword"
              }
            }
          },
          "anUploadFiles": {
            "properties": {
              "fileName": {
                "type": "keyword"
              },
              "filePath": {
                "type": "keyword"
              }
            }
          },
          "anMatrixRadios": {
            "properties": {
              "rowDwId": {
                "type": "keyword"
              },
              "colDwId": {
                "type": "keyword"
              },
              "rowAnScore": {
                "type": "float"
              }
            }
          },
          "anMatrixCheckboxes": {
            "properties": {
              "rowDwId": {
                "type": "keyword"
              },
              "rowAnCheckboxs": {
                "properties": {
                  "optionDwId": {
                    "type": "keyword"
                  },
                  "otherText": {
                    "analyzer": "ik_max_word",
                    "type": "text",
                    "fields": {
                      "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                      }
                    }
                  }
                }
              },
              "rowAnScore": {
                "type": "float"
              }
            }
          },
          "anMatrixFbks": {
            "properties": {
              "rowDwId": {
                "type": "keyword"
              },
              "rowAnFbks": {
                "properties": {
                  "answer": {
                    "analyzer": "ik_max_word",
                    "type": "text",
                    "fields": {
                      "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                      }
                    }
                  },
                  "optionDwId": {
                    "type": "keyword"
                  }
                }
              }
            }
          },
          "anMatrixScales": {
            "properties": {
              "rowDwId": {
                "type": "keyword"
              },
              "answerScore": {
                "type": "float"
              },
              "rowAnScore": {
                "type": "float"
              }
            }
          },
          "quDwId": {
            "type": "keyword"
          },
          "quType": {
            "type": "keyword"
          },
          "quAnScore": {
            "type": "float"
          }
        }
      }
    }
  }
}