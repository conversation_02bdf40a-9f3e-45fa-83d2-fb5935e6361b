<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE urlrewrite PUBLIC "-//tuckey.org//DTD UrlRewrite 4.0//EN"
        "http://www.tuckey.org/res/dtds/urlrewrite4.0.dtd">

<!--

    Configuration file for UrlRewriteFilter
    http://www.tuckey.org/urlrewrite/

-->
<urlrewrite>
		<!-- <rule>
              <from>/(.*).html</from>
              <to>test/freemarker-test.jsp?id=$1</to>
       </rule> -->
       <rule>
              <from>/resp/(.*)/(.*)/(.*)/(.*).html</from>
              <to>/nosm/response!input.action?year=$1&amp;month=$2&amp;day=$3&amp;parentId=$4</to>
       </rule>
    <rule>
        <from>/diaowen/(.*).html</from>
        <to>/response/dwsurvey.do?sid=$1</to>
    </rule>
   <!--
    <rule>
        <note>
            The rule means that requests to /test/status/ will be redirected to /rewrite-status
            the url will be rewritten.
        </note>
        <from>/test/status/</from>
        <to type="redirect">%{context-path}/rewrite-status</to>
    </rule>


    <outbound-rule>
        <note>
            The outbound-rule specifies that when response.encodeURL is called (if you are using JSTL c:url)
            the url /rewrite-status will be rewritten to /test/status/.

            The above rule and this outbound-rule means that end users should never see the
            url /rewrite-status only /test/status/ both in thier location bar and in hyperlinks
            in your pages.
        </note>
        <from>/rewrite-status</from>
        <to>/test/status/</to>
    </outbound-rule>


    

    INSTALLATION

        in your web.xml add...

        <filter>
            <filter-name>UrlRewriteFilter</filter-name>
            <filter-class>org.tuckey.web.filters.urlrewrite.UrlRewriteFilter</filter-class>
            <init-param>
                <param-name>logLevel</param-name>
                <param-value>WARN</param-value>
            </init-param>
        </filter>
        <filter-mapping>
            <filter-name>UrlRewriteFilter</filter-name>
            <url-pattern>/*</url-pattern>
        </filter-mapping>

     EXAMPLES

     Redirect one url
        <rule>
            <from>/some/old/page.html</from>
            <to type="redirect">/very/new/page.html</to>
        </rule>

    Redirect a directory
        <rule>
            <from>/some/olddir/(.*)</from>
            <to type="redirect">/very/newdir/$1</to>
        </rule>

    Clean a url
        <rule>
            <from>/products/([0-9]+)</from>
            <to>/products/index.jsp?product_id=$1</to>
        </rule>
    eg, /products/1234 will be passed on to /products/index.jsp?product_id=1234 without the user noticing.

    Browser detection
        <rule>
            <condition name="user-agent">Mozilla/[1-4]</condition>
            <from>/some/page.html</from>
            <to>/some/page-for-old-browsers.html</to>
        </rule>
    eg, will pass the request for /some/page.html on to /some/page-for-old-browsers.html only for older
    browsers whose user agent srtings match Mozilla/1, Mozilla/2, Mozilla/3 or Mozilla/4.

    Centralised browser detection
        <rule>
            <condition name="user-agent">Mozilla/[1-4]</condition>
            <set type="request" name="browser">moz</set>
        </rule>
    eg, all requests will be checked against the condition and if matched
    request.setAttribute("browser", "moz") will be called.

    -->

</urlrewrite>

